# South Safari Developer Partnership Platform

![South Safari Logo](public/assets/images/logo.png)

**Version:** 1.0.0  
**License:** MIT  
**Author:** South Safari Team

## 🌍 Overview

South Safari is a comprehensive PHP-based partnership platform designed to connect South African businesses with talented developers from Bangladesh, India, Pakistan, and other countries. The platform facilitates partnerships for introducing developer products to the Southern African market.

### Key Features

- **Partnership Opportunities**: <PERSON>rowse and apply for business partnership projects
- **Developer Profiles**: Comprehensive developer registration and profile management
- **Project Management**: Full project lifecycle management for administrators
- **Communication System**: Built-in messaging and email integration
- **Document Management**: Secure file upload and sharing capabilities
- **Admin Dashboard**: Complete administrative control panel
- **Responsive Design**: Mobile-first, professional interface
- **One-Click Installation**: Easy setup with XAMPP compatibility

## 🚀 Quick Start

### System Requirements

- **PHP**: 7.4 or higher
- **MySQL/MariaDB**: 5.7 or higher
- **Apache/Nginx**: Latest stable version
- **Disk Space**: 500MB minimum
- **Memory**: 512MB RAM minimum

### Required PHP Extensions

- PDO and PDO_MySQL
- mbstring
- openssl
- curl
- gd or imagick
- fileinfo
- zip
- json

### XAMPP Installation

1. **Download XAMPP** (7.4 or higher) from [https://www.apachefriends.org/](https://www.apachefriends.org/)
2. **Install XAMPP** and start Apache and MySQL services
3. **Extract** the South Safari platform to `C:\xampp\htdocs\south-safari\`
4. **Open browser** and navigate to `http://localhost/south-safari/install/`
5. **Follow the installer** to complete setup

### Manual Installation

```bash
# 1. Clone or extract the project
cd /path/to/xampp/htdocs/
unzip south-safari.zip
cd south-safari/

# 2. Set up database
mysql -u root -p
CREATE DATABASE south_safari CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
mysql -u root -p south_safari < database/schema.sql

# 3. Configure application
cp config/config.template.php config/config.php
# Edit config/config.php with your database settings

# 4. Set permissions
chmod 755 public/uploads/
chmod 755 storage/logs/
chmod 644 config/config.php
```

## 📁 Project Structure

```
south-safari/
├── config/                 # Configuration files
│   ├── config.template.php # Configuration template
│   └── config.php          # Main configuration (created during install)
├── database/               # Database files
│   ├── schema.sql          # Database schema
│   └── migrations/         # Database migrations
├── docs/                   # Documentation
│   ├── PRD.md             # Product Requirements Document
│   ├── Technical_Architecture.md
│   ├── Database_Schema.md
│   └── Installation_Guide.md
├── install/                # Installation files
│   └── installer.php      # One-click installer
├── public/                 # Web root directory
│   ├── assets/            # CSS, JS, images
│   ├── uploads/           # User uploaded files
│   ├── index.php          # Main entry point
│   └── .htaccess          # Apache configuration
├── src/                    # Application source code
│   ├── Controllers/       # MVC Controllers
│   ├── Core/              # Core framework classes
│   ├── Middleware/        # Request middleware
│   ├── Models/            # Data models (future)
│   └── Views/             # Template files
├── storage/                # Application storage
│   ├── cache/             # Cache files
│   └── logs/              # Log files
└── vendor/                 # Third-party dependencies
```

## 🔧 Configuration

### Database Configuration

Edit `config/config.php`:

```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'south_safari');
define('DB_USER', 'root');
define('DB_PASS', '');
```

### Email Configuration

```php
define('MAIL_DRIVER', 'smtp');
define('MAIL_HOST', 'smtp.gmail.com');
define('MAIL_PORT', 587);
define('MAIL_USERNAME', '<EMAIL>');
define('MAIL_PASSWORD', 'your-app-password');
define('MAIL_ENCRYPTION', 'tls');
```

### Security Settings

```php
define('SECRET_KEY', 'your-unique-secret-key');
define('SESSION_LIFETIME', 7200); // 2 hours
define('PASSWORD_MIN_LENGTH', 8);
```

## 👥 User Roles

### Administrator
- Full platform management
- Project creation and management
- User management
- Application review and approval
- Partnership management
- System settings

### Developer
- Browse partnership opportunities
- Submit applications
- Manage profile and portfolio
- Communicate with project owners
- Track application status

## 🎯 Core Features

### For Developers
- **Project Discovery**: Browse categorized partnership opportunities
- **Application System**: Submit detailed applications with portfolios
- **Profile Management**: Comprehensive developer profiles
- **Communication**: Direct messaging with project owners
- **Dashboard**: Track applications and partnerships

### For Administrators
- **Project Management**: Create and manage partnership opportunities
- **Application Review**: Review and manage developer applications
- **User Management**: Manage developer accounts and permissions
- **Analytics**: Track platform performance and engagement
- **Communication**: Email integration and messaging system

## 🔐 Security Features

- **CSRF Protection**: Cross-site request forgery prevention
- **SQL Injection Prevention**: Prepared statements and parameterized queries
- **XSS Protection**: Input sanitization and output encoding
- **Session Security**: Secure session management with regeneration
- **File Upload Security**: Type validation and secure storage
- **Password Security**: Strong password hashing with PHP's password_hash()

## 🌐 API Endpoints

### Public API
- `GET /api/v1/projects` - List projects
- `GET /api/v1/projects/{id}` - Get project details
- `GET /api/v1/categories` - List categories
- `POST /api/v1/applications` - Submit application

### Authentication Required
- `GET /api/v1/dashboard` - User dashboard data
- `POST /api/v1/messages` - Send message
- `POST /api/v1/upload` - Upload files

## 🧪 Testing

### Manual Testing Checklist

- [ ] Installation process works correctly
- [ ] User registration and login
- [ ] Project browsing and search
- [ ] Application submission
- [ ] Admin dashboard functionality
- [ ] Email functionality
- [ ] File upload capabilities
- [ ] Responsive design on mobile

### Browser Compatibility

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 🚀 Deployment

### Production Checklist

1. **Security**
   - [ ] Change default admin password
   - [ ] Update SECRET_KEY in config
   - [ ] Configure SSL certificate
   - [ ] Remove or secure install directory
   - [ ] Set proper file permissions

2. **Performance**
   - [ ] Enable caching
   - [ ] Optimize images
   - [ ] Configure CDN (optional)
   - [ ] Set up database indexing

3. **Monitoring**
   - [ ] Configure error logging
   - [ ] Set up backup procedures
   - [ ] Monitor disk space
   - [ ] Configure email alerts

## 📚 Documentation

- [Product Requirements Document](docs/PRD.md)
- [Technical Architecture](docs/Technical_Architecture.md)
- [Database Schema](docs/Database_Schema.md)
- [Installation Guide](docs/Installation_Guide.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

- **Email**: <EMAIL>
- **Documentation**: Check the `docs/` directory
- **Issues**: Report bugs and feature requests

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Bootstrap for the responsive framework
- Font Awesome for icons
- PHP community for excellent documentation
- All contributors and testers

---

**South Safari Platform** - Connecting South African opportunities with global talent.

For more information, visit our [documentation](docs/) or contact our support team.
