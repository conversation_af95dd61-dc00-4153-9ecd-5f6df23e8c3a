<?php
/**
 * South Safari Platform - Configuration Template
 * 
 * This file contains all configuration settings for the South Safari platform.
 * Copy this file to config.php and update the values according to your environment.
 */

// Prevent direct access
if (!defined('SOUTH_SAFARI')) {
    die('Direct access not permitted');
}

// =============================================================================
// DATABASE CONFIGURATION
// =============================================================================

define('DB_HOST', 'localhost');
define('DB_NAME', 'south_safari');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');
define('DB_PREFIX', 'ss_');

// =============================================================================
// APPLICATION CONFIGURATION
// =============================================================================

define('APP_NAME', 'South Safari');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://localhost/south-safari');
define('APP_TIMEZONE', 'Africa/Johannesburg');
define('APP_LOCALE', 'en_US');

// =============================================================================
// SECURITY CONFIGURATION
// =============================================================================

define('SECRET_KEY', 'your-secret-key-here-change-this');
define('CSRF_TOKEN_NAME', 'csrf_token');
define('SESSION_NAME', 'south_safari_session');
define('SESSION_LIFETIME', 7200); // 2 hours in seconds
define('PASSWORD_MIN_LENGTH', 8);

// =============================================================================
// EMAIL CONFIGURATION
// =============================================================================

define('MAIL_DRIVER', 'smtp'); // smtp, mail, sendmail
define('MAIL_HOST', 'smtp.gmail.com');
define('MAIL_PORT', 587);
define('MAIL_USERNAME', '<EMAIL>');
define('MAIL_PASSWORD', 'your-app-password');
define('MAIL_ENCRYPTION', 'tls'); // tls, ssl, or empty
define('MAIL_FROM_ADDRESS', '<EMAIL>');
define('MAIL_FROM_NAME', 'South Safari Platform');

// =============================================================================
// FILE UPLOAD CONFIGURATION
// =============================================================================

define('UPLOAD_MAX_SIZE', 50 * 1024 * 1024); // 50MB in bytes
define('UPLOAD_ALLOWED_TYPES', 'jpg,jpeg,png,gif,pdf,doc,docx,txt,zip,rar');
define('UPLOAD_PATH', 'public/uploads/');
define('AVATAR_MAX_SIZE', 5 * 1024 * 1024); // 5MB for profile images

// =============================================================================
// PAGINATION CONFIGURATION
// =============================================================================

define('ITEMS_PER_PAGE', 20);
define('PROJECTS_PER_PAGE', 12);
define('MESSAGES_PER_PAGE', 15);
define('APPLICATIONS_PER_PAGE', 25);

// =============================================================================
// CACHE CONFIGURATION
// =============================================================================

define('CACHE_ENABLED', true);
define('CACHE_LIFETIME', 3600); // 1 hour in seconds
define('CACHE_PATH', 'storage/cache/');

// =============================================================================
// DEBUG AND LOGGING
// =============================================================================

define('DEBUG_MODE', false);
define('LOG_ERRORS', true);
define('LOG_PATH', 'storage/logs/');
define('LOG_LEVEL', 'error'); // debug, info, warning, error

// =============================================================================
// API CONFIGURATION
// =============================================================================

define('API_ENABLED', true);
define('API_RATE_LIMIT', 100); // requests per hour
define('API_VERSION', 'v1');

// =============================================================================
// SOCIAL MEDIA INTEGRATION (Optional)
// =============================================================================

define('FACEBOOK_APP_ID', '');
define('TWITTER_API_KEY', '');
define('LINKEDIN_CLIENT_ID', '');
define('GOOGLE_CLIENT_ID', '');

// =============================================================================
// NOTIFICATION SETTINGS
// =============================================================================

define('NOTIFY_NEW_APPLICATION', true);
define('NOTIFY_MESSAGE_RECEIVED', true);
define('NOTIFY_PROJECT_UPDATES', true);
define('NOTIFY_PARTNERSHIP_UPDATES', true);

// =============================================================================
// SYSTEM PATHS
// =============================================================================

define('ROOT_PATH', dirname(__DIR__));
define('APP_PATH', ROOT_PATH . '/src');
define('PUBLIC_PATH', ROOT_PATH . '/public');
define('STORAGE_PATH', ROOT_PATH . '/storage');
define('CONFIG_PATH', ROOT_PATH . '/config');
define('VENDOR_PATH', ROOT_PATH . '/vendor');

// =============================================================================
// URL CONFIGURATION
// =============================================================================

define('BASE_URL', APP_URL);
define('ADMIN_URL', APP_URL . '/admin');
define('API_URL', APP_URL . '/api');
define('ASSETS_URL', APP_URL . '/assets');
define('UPLOADS_URL', APP_URL . '/uploads');

// =============================================================================
// FEATURE FLAGS
// =============================================================================

define('FEATURE_REGISTRATION', true);
define('FEATURE_EMAIL_VERIFICATION', true);
define('FEATURE_TWO_FACTOR_AUTH', false);
define('FEATURE_SOCIAL_LOGIN', false);
define('FEATURE_ANALYTICS', true);
define('FEATURE_MAINTENANCE_MODE', false);

// =============================================================================
// BUSINESS LOGIC SETTINGS
// =============================================================================

define('MAX_APPLICATIONS_PER_PROJECT', 100);
define('MAX_PROJECTS_PER_ADMIN', 50);
define('APPLICATION_REVIEW_DAYS', 14);
define('PROJECT_EXPIRY_DAYS', 90);
define('PARTNERSHIP_DURATION_MONTHS', 12);

// =============================================================================
// LOCALIZATION
// =============================================================================

define('DEFAULT_LANGUAGE', 'en');
define('SUPPORTED_LANGUAGES', 'en,af,zu'); // English, Afrikaans, Zulu
define('DATE_FORMAT', 'Y-m-d');
define('TIME_FORMAT', 'H:i:s');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');

// =============================================================================
// THIRD-PARTY INTEGRATIONS
// =============================================================================

define('GOOGLE_ANALYTICS_ID', '');
define('RECAPTCHA_SITE_KEY', '');
define('RECAPTCHA_SECRET_KEY', '');
define('STRIPE_PUBLIC_KEY', '');
define('STRIPE_SECRET_KEY', '');

// =============================================================================
// BACKUP CONFIGURATION
// =============================================================================

define('BACKUP_ENABLED', true);
define('BACKUP_PATH', 'storage/backups/');
define('BACKUP_RETENTION_DAYS', 30);
define('BACKUP_SCHEDULE', 'daily'); // daily, weekly, monthly

// =============================================================================
// PERFORMANCE SETTINGS
// =============================================================================

define('ENABLE_GZIP', true);
define('ENABLE_MINIFICATION', true);
define('ENABLE_CDN', false);
define('CDN_URL', '');

// =============================================================================
// SECURITY HEADERS
// =============================================================================

define('SECURITY_HEADERS', [
    'X-Content-Type-Options' => 'nosniff',
    'X-Frame-Options' => 'DENY',
    'X-XSS-Protection' => '1; mode=block',
    'Referrer-Policy' => 'strict-origin-when-cross-origin',
    'Content-Security-Policy' => "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';"
]);

// =============================================================================
// CUSTOM SETTINGS
// =============================================================================

// Add any custom configuration settings here
define('CUSTOM_FOOTER_TEXT', 'Connecting South African opportunities with global talent');
define('CONTACT_EMAIL', '<EMAIL>');
define('SUPPORT_EMAIL', '<EMAIL>');
define('COMPANY_ADDRESS', 'Cape Town, South Africa');

?>
