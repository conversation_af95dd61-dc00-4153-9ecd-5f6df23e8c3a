-- South Safari Platform Database Schema
-- Version: 1.0.0
-- Created: 2025-07-07

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `ss_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `role` enum('admin','developer') DEFAULT 'developer',
  `status` enum('active','inactive','pending') DEFAULT 'pending',
  `country` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `company_name` varchar(255) DEFAULT NULL,
  `website` varchar(255) DEFAULT NULL,
  `bio` text DEFAULT NULL,
  `profile_image` varchar(255) DEFAULT NULL,
  `email_verified` tinyint(1) DEFAULT 0,
  `email_verification_token` varchar(255) DEFAULT NULL,
  `password_reset_token` varchar(255) DEFAULT NULL,
  `password_reset_expires` datetime DEFAULT NULL,
  `last_login` datetime DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`),
  KEY `idx_country` (`country`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `categories`
--

CREATE TABLE `ss_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `icon` varchar(100) DEFAULT NULL,
  `sort_order` int(11) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  UNIQUE KEY `slug` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `projects`
--

CREATE TABLE `ss_projects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `detailed_description` longtext DEFAULT NULL,
  `category` varchar(100) NOT NULL,
  `compensation_type` enum('fixed','percentage','hybrid','negotiable') NOT NULL,
  `compensation_details` text DEFAULT NULL,
  `requirements` text DEFAULT NULL,
  `skills_required` json DEFAULT NULL,
  `timeline` varchar(100) DEFAULT NULL,
  `budget_range` varchar(100) DEFAULT NULL,
  `status` enum('draft','published','closed','archived') DEFAULT 'draft',
  `featured` tinyint(1) DEFAULT 0,
  `application_deadline` date DEFAULT NULL,
  `project_start_date` date DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `views_count` int(11) DEFAULT 0,
  `applications_count` int(11) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `published_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`),
  KEY `idx_status` (`status`),
  KEY `idx_category` (`category`),
  KEY `idx_featured` (`featured`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_published_at` (`published_at`),
  CONSTRAINT `fk_projects_created_by` FOREIGN KEY (`created_by`) REFERENCES `ss_users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `applications`
--

CREATE TABLE `ss_applications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `project_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `status` enum('pending','under_review','shortlisted','accepted','rejected') DEFAULT 'pending',
  `cover_letter` text NOT NULL,
  `relevant_experience` text DEFAULT NULL,
  `proposed_timeline` varchar(100) DEFAULT NULL,
  `proposed_compensation` text DEFAULT NULL,
  `portfolio_links` json DEFAULT NULL,
  `additional_notes` text DEFAULT NULL,
  `admin_notes` text DEFAULT NULL,
  `reviewed_by` int(11) DEFAULT NULL,
  `reviewed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_application` (`project_id`,`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_applications_project` FOREIGN KEY (`project_id`) REFERENCES `ss_projects` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_applications_user` FOREIGN KEY (`user_id`) REFERENCES `ss_users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_applications_reviewed_by` FOREIGN KEY (`reviewed_by`) REFERENCES `ss_users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `messages`
--

CREATE TABLE `ss_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `thread_id` varchar(100) NOT NULL,
  `sender_id` int(11) NOT NULL,
  `recipient_id` int(11) NOT NULL,
  `subject` varchar(255) DEFAULT NULL,
  `message` text NOT NULL,
  `message_type` enum('internal','email','system') DEFAULT 'internal',
  `is_read` tinyint(1) DEFAULT 0,
  `parent_message_id` int(11) DEFAULT NULL,
  `related_project_id` int(11) DEFAULT NULL,
  `related_application_id` int(11) DEFAULT NULL,
  `attachments` json DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_thread_id` (`thread_id`),
  KEY `idx_sender_id` (`sender_id`),
  KEY `idx_recipient_id` (`recipient_id`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_messages_sender` FOREIGN KEY (`sender_id`) REFERENCES `ss_users` (`id`),
  CONSTRAINT `fk_messages_recipient` FOREIGN KEY (`recipient_id`) REFERENCES `ss_users` (`id`),
  CONSTRAINT `fk_messages_parent` FOREIGN KEY (`parent_message_id`) REFERENCES `ss_messages` (`id`),
  CONSTRAINT `fk_messages_project` FOREIGN KEY (`related_project_id`) REFERENCES `ss_projects` (`id`),
  CONSTRAINT `fk_messages_application` FOREIGN KEY (`related_application_id`) REFERENCES `ss_applications` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `documents`
--

CREATE TABLE `ss_documents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `filename` varchar(255) NOT NULL,
  `original_filename` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` int(11) NOT NULL,
  `mime_type` varchar(100) NOT NULL,
  `file_hash` varchar(64) DEFAULT NULL,
  `document_type` enum('application','agreement','portfolio','project','general') NOT NULL,
  `uploaded_by` int(11) NOT NULL,
  `related_project_id` int(11) DEFAULT NULL,
  `related_application_id` int(11) DEFAULT NULL,
  `related_partnership_id` int(11) DEFAULT NULL,
  `is_public` tinyint(1) DEFAULT 0,
  `download_count` int(11) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_document_type` (`document_type`),
  KEY `idx_uploaded_by` (`uploaded_by`),
  KEY `idx_file_hash` (`file_hash`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_documents_uploaded_by` FOREIGN KEY (`uploaded_by`) REFERENCES `ss_users` (`id`),
  CONSTRAINT `fk_documents_project` FOREIGN KEY (`related_project_id`) REFERENCES `ss_projects` (`id`),
  CONSTRAINT `fk_documents_application` FOREIGN KEY (`related_application_id`) REFERENCES `ss_applications` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `partnerships`
--

CREATE TABLE `ss_partnerships` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `project_id` int(11) NOT NULL,
  `developer_id` int(11) NOT NULL,
  `application_id` int(11) NOT NULL,
  `status` enum('negotiating','active','completed','terminated','on_hold') DEFAULT 'negotiating',
  `partnership_type` varchar(100) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `compensation_agreed` text DEFAULT NULL,
  `terms_and_conditions` text DEFAULT NULL,
  `milestones` json DEFAULT NULL,
  `progress_notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_developer_id` (`developer_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_partnerships_project` FOREIGN KEY (`project_id`) REFERENCES `ss_projects` (`id`),
  CONSTRAINT `fk_partnerships_developer` FOREIGN KEY (`developer_id`) REFERENCES `ss_users` (`id`),
  CONSTRAINT `fk_partnerships_application` FOREIGN KEY (`application_id`) REFERENCES `ss_applications` (`id`),
  CONSTRAINT `fk_partnerships_created_by` FOREIGN KEY (`created_by`) REFERENCES `ss_users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `ss_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `setting_type` enum('string','integer','boolean','json') DEFAULT 'string',
  `description` text DEFAULT NULL,
  `is_public` tinyint(1) DEFAULT 0,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `activity_logs`
--

CREATE TABLE `ss_activity_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `related_table` varchar(50) DEFAULT NULL,
  `related_id` int(11) DEFAULT NULL,
  `old_values` json DEFAULT NULL,
  `new_values` json DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_activity_logs_user` FOREIGN KEY (`user_id`) REFERENCES `ss_users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Insert default categories
--

INSERT INTO `ss_categories` (`name`, `slug`, `description`, `icon`, `sort_order`, `is_active`) VALUES
('Web Development', 'web-development', 'Web applications, websites, and web services', 'fas fa-globe', 1, 1),
('Mobile Development', 'mobile-development', 'iOS, Android, and cross-platform mobile applications', 'fas fa-mobile-alt', 2, 1),
('Software Development', 'software-development', 'Desktop applications and enterprise software', 'fas fa-desktop', 3, 1),
('E-commerce Solutions', 'ecommerce-solutions', 'Online stores and e-commerce platforms', 'fas fa-shopping-cart', 4, 1),
('Digital Marketing Tools', 'digital-marketing-tools', 'Marketing automation and analytics tools', 'fas fa-chart-line', 5, 1),
('SaaS Products', 'saas-products', 'Software as a Service solutions', 'fas fa-cloud', 6, 1);

-- --------------------------------------------------------

--
-- Insert default settings
--

INSERT INTO `ss_settings` (`setting_key`, `setting_value`, `setting_type`, `description`, `is_public`) VALUES
('site_name', 'South Safari', 'string', 'Website name', 1),
('site_description', 'Connecting South African opportunities with global talent', 'string', 'Website description', 1),
('admin_email', '<EMAIL>', 'string', 'Administrator email address', 0),
('registration_enabled', '1', 'boolean', 'Allow new user registration', 0),
('email_verification_required', '1', 'boolean', 'Require email verification for new accounts', 0),
('max_applications_per_project', '100', 'integer', 'Maximum applications allowed per project', 0),
('application_review_days', '14', 'integer', 'Days to review applications', 0),
('featured_projects_limit', '6', 'integer', 'Number of featured projects on homepage', 1);

COMMIT;
