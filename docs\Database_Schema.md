# South Safari Platform - Database Schema Design

## 1. Database Overview

### 1.1 Database Information
- **Database Engine**: MySQL/MariaDB
- **Character Set**: utf8mb4
- **Collation**: utf8mb4_unicode_ci
- **Storage Engine**: InnoDB (for foreign key support)

### 1.2 Design Principles
- Normalized database design (3NF)
- Consistent naming conventions
- Proper indexing for performance
- Foreign key constraints for data integrity
- Soft deletes for important records

## 2. Core Tables

### 2.1 Users Table
```sql
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VA<PERSON>HA<PERSON>(100) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    role ENUM('admin', 'developer') DEFAULT 'developer',
    status ENUM('active', 'inactive', 'pending') DEFAULT 'pending',
    country VARCHAR(100),
    phone VARCHAR(20),
    company_name VA<PERSON>HA<PERSON>(255),
    website VARCHAR(255),
    bio TEXT,
    profile_image VARCHAR(255),
    email_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255),
    password_reset_token VARCHAR(255),
    password_reset_expires DATETIME,
    last_login DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status),
    INDEX idx_country (country)
);
```

### 2.2 Projects Table
```sql
CREATE TABLE projects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT NOT NULL,
    detailed_description LONGTEXT,
    category VARCHAR(100) NOT NULL,
    compensation_type ENUM('fixed', 'percentage', 'hybrid', 'negotiable') NOT NULL,
    compensation_details TEXT,
    requirements TEXT,
    skills_required JSON,
    timeline VARCHAR(100),
    budget_range VARCHAR(100),
    status ENUM('draft', 'published', 'closed', 'archived') DEFAULT 'draft',
    featured BOOLEAN DEFAULT FALSE,
    application_deadline DATE,
    project_start_date DATE,
    created_by INT NOT NULL,
    views_count INT DEFAULT 0,
    applications_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    published_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_status (status),
    INDEX idx_category (category),
    INDEX idx_featured (featured),
    INDEX idx_created_by (created_by),
    INDEX idx_published_at (published_at)
);
```

### 2.3 Applications Table
```sql
CREATE TABLE applications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    project_id INT NOT NULL,
    user_id INT NOT NULL,
    status ENUM('pending', 'under_review', 'shortlisted', 'accepted', 'rejected') DEFAULT 'pending',
    cover_letter TEXT NOT NULL,
    relevant_experience TEXT,
    proposed_timeline VARCHAR(100),
    proposed_compensation TEXT,
    portfolio_links JSON,
    additional_notes TEXT,
    admin_notes TEXT,
    reviewed_by INT,
    reviewed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_by) REFERENCES users(id),
    UNIQUE KEY unique_application (project_id, user_id),
    INDEX idx_status (status),
    INDEX idx_project_id (project_id),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
);
```

### 2.4 Messages Table
```sql
CREATE TABLE messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    thread_id VARCHAR(100) NOT NULL,
    sender_id INT NOT NULL,
    recipient_id INT NOT NULL,
    subject VARCHAR(255),
    message TEXT NOT NULL,
    message_type ENUM('internal', 'email', 'system') DEFAULT 'internal',
    is_read BOOLEAN DEFAULT FALSE,
    parent_message_id INT,
    related_project_id INT,
    related_application_id INT,
    attachments JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    FOREIGN KEY (sender_id) REFERENCES users(id),
    FOREIGN KEY (recipient_id) REFERENCES users(id),
    FOREIGN KEY (parent_message_id) REFERENCES messages(id),
    FOREIGN KEY (related_project_id) REFERENCES projects(id),
    FOREIGN KEY (related_application_id) REFERENCES applications(id),
    INDEX idx_thread_id (thread_id),
    INDEX idx_sender_id (sender_id),
    INDEX idx_recipient_id (recipient_id),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at)
);
```

### 2.5 Documents Table
```sql
CREATE TABLE documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_hash VARCHAR(64),
    document_type ENUM('application', 'agreement', 'portfolio', 'project', 'general') NOT NULL,
    uploaded_by INT NOT NULL,
    related_project_id INT,
    related_application_id INT,
    related_partnership_id INT,
    is_public BOOLEAN DEFAULT FALSE,
    download_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    FOREIGN KEY (uploaded_by) REFERENCES users(id),
    FOREIGN KEY (related_project_id) REFERENCES projects(id),
    FOREIGN KEY (related_application_id) REFERENCES applications(id),
    INDEX idx_document_type (document_type),
    INDEX idx_uploaded_by (uploaded_by),
    INDEX idx_file_hash (file_hash),
    INDEX idx_created_at (created_at)
);
```

### 2.6 Partnerships Table
```sql
CREATE TABLE partnerships (
    id INT PRIMARY KEY AUTO_INCREMENT,
    project_id INT NOT NULL,
    developer_id INT NOT NULL,
    application_id INT NOT NULL,
    status ENUM('negotiating', 'active', 'completed', 'terminated', 'on_hold') DEFAULT 'negotiating',
    partnership_type VARCHAR(100),
    start_date DATE,
    end_date DATE,
    compensation_agreed TEXT,
    terms_and_conditions TEXT,
    milestones JSON,
    progress_notes TEXT,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (project_id) REFERENCES projects(id),
    FOREIGN KEY (developer_id) REFERENCES users(id),
    FOREIGN KEY (application_id) REFERENCES applications(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_status (status),
    INDEX idx_project_id (project_id),
    INDEX idx_developer_id (developer_id),
    INDEX idx_created_at (created_at)
);
```

## 3. Supporting Tables

### 3.1 Categories Table
```sql
CREATE TABLE categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) UNIQUE NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    icon VARCHAR(100),
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 3.2 Settings Table
```sql
CREATE TABLE settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'integer', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 3.3 Activity Log Table
```sql
CREATE TABLE activity_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    related_table VARCHAR(50),
    related_id INT,
    old_values JSON,
    new_values JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
);
```

## 4. Database Relationships

### 4.1 Primary Relationships
- **users** → **projects** (1:n) - Admin creates projects
- **projects** → **applications** (1:n) - Projects receive applications
- **users** → **applications** (1:n) - Developers submit applications
- **applications** → **partnerships** (1:1) - Successful applications become partnerships
- **users** → **messages** (1:n) - Users send/receive messages
- **users** → **documents** (1:n) - Users upload documents

### 4.2 Junction Tables
- **applications** serves as junction between **users** and **projects**
- **partnerships** represents formalized relationships
- **messages** enables communication between users

## 5. Indexes and Performance

### 5.1 Primary Indexes
- All tables have primary key indexes
- Foreign key columns are automatically indexed
- Unique constraints create unique indexes

### 5.2 Additional Indexes
- Composite indexes for common query patterns
- Text search indexes for search functionality
- Date indexes for time-based queries

## 6. Data Integrity

### 6.1 Constraints
- Foreign key constraints maintain referential integrity
- Check constraints for enum values
- Unique constraints prevent duplicates
- NOT NULL constraints for required fields

### 6.2 Triggers (Optional)
- Update timestamps automatically
- Maintain counters (views, applications)
- Log important changes

---

**Document Version**: 1.0  
**Last Updated**: 2025-07-07  
**Status**: Draft
