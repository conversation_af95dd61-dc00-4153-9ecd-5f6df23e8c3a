# South Safari Platform - Installation & Setup Guide

## 1. System Requirements

### 1.1 Minimum Requirements
- **PHP**: 7.4 or higher
- **MySQL/MariaDB**: 5.7 or higher
- **Apache/Nginx**: Latest stable version
- **Disk Space**: 500MB minimum
- **Memory**: 512MB RAM minimum

### 1.2 Required PHP Extensions
- PDO and PDO_MySQL
- mbstring
- openssl
- curl
- gd or imagick
- fileinfo
- zip
- json

### 1.3 XAMPP Compatibility
- **XAMPP Version**: 7.4.x or higher
- **Apache**: mod_rewrite enabled
- **MySQL**: InnoDB storage engine
- **PHP**: All required extensions included

## 2. One-Click Installation Process

### 2.1 Installation Steps
1. **Download & Extract**: Extract the South Safari package to your web directory
2. **Access Installer**: Navigate to `http://localhost/south-safari/install/`
3. **System Check**: Installer verifies system requirements
4. **Database Setup**: Configure database connection
5. **Admin Account**: Create the administrator account
6. **Configuration**: Generate configuration files
7. **Finalization**: Complete installation and cleanup

### 2.2 Installation Directory Structure
```
south-safari/
├── install/
│   ├── installer.php          # Main installation script
│   ├── requirements.php       # System requirements checker
│   ├── database.sql          # Database schema
│   ├── sample_data.sql       # Optional sample data
│   └── assets/               # Installation assets
├── config/
│   └── config.template.php   # Configuration template
└── [application files]
```

## 3. Manual Installation (Alternative)

### 3.1 Step 1: File Setup
```bash
# Extract files to web directory
cd /path/to/xampp/htdocs/
unzip south-safari.zip
cd south-safari/
```

### 3.2 Step 2: Database Setup
```sql
-- Create database
CREATE DATABASE south_safari CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user (optional)
CREATE USER 'safari_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON south_safari.* TO 'safari_user'@'localhost';
FLUSH PRIVILEGES;

-- Import schema
mysql -u root -p south_safari < database/schema.sql
```

### 3.3 Step 3: Configuration
```php
// Copy and edit configuration file
cp config/config.template.php config/config.php

// Edit database settings in config/config.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'south_safari');
define('DB_USER', 'safari_user');
define('DB_PASS', 'secure_password');
```

### 3.4 Step 4: Permissions
```bash
# Set proper permissions
chmod 755 public/uploads/
chmod 755 storage/logs/
chmod 644 config/config.php
```

## 4. XAMPP-Specific Setup

### 4.1 XAMPP Installation
1. **Start XAMPP**: Launch XAMPP Control Panel
2. **Start Services**: Start Apache and MySQL
3. **Extract Files**: Place South Safari in `htdocs/south-safari/`
4. **Access Installer**: Go to `http://localhost/south-safari/install/`

### 4.2 XAMPP Configuration
```apache
# .htaccess configuration (automatically included)
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
```

### 4.3 PHP Configuration
```ini
# Recommended php.ini settings
upload_max_filesize = 50M
post_max_size = 50M
max_execution_time = 300
memory_limit = 256M
date.timezone = Africa/Johannesburg
```

## 5. Installation Wizard Details

### 5.1 Welcome Screen
- Introduction to South Safari Platform
- System requirements overview
- Installation process explanation

### 5.2 Requirements Check
```php
// Automatic checks performed:
- PHP version compatibility
- Required PHP extensions
- Directory permissions
- Database connectivity
- Memory and disk space
```

### 5.3 Database Configuration
- **Host**: Database server (usually localhost)
- **Database Name**: Name for the South Safari database
- **Username**: Database user credentials
- **Password**: Database password
- **Table Prefix**: Optional table prefix (default: ss_)

### 5.4 Admin Account Setup
- **Username**: Administrator username
- **Email**: Administrator email address
- **Password**: Secure administrator password
- **First Name**: Administrator's first name
- **Last Name**: Administrator's last name

### 5.5 Site Configuration
- **Site Name**: Platform name (default: South Safari)
- **Site URL**: Base URL of the installation
- **Email Settings**: SMTP configuration (optional)
- **Timezone**: Site timezone setting

## 6. Post-Installation Setup

### 6.1 Security Checklist
- [ ] Remove or secure the `/install/` directory
- [ ] Change default admin password
- [ ] Configure SSL certificate (recommended)
- [ ] Set up regular backups
- [ ] Configure email settings
- [ ] Review file permissions

### 6.2 Initial Configuration
1. **Login**: Access admin dashboard at `/admin/`
2. **Email Setup**: Configure SMTP settings
3. **Site Settings**: Customize site information
4. **Categories**: Set up project categories
5. **Sample Content**: Add initial projects (optional)

### 6.3 Testing Installation
- [ ] Admin login functionality
- [ ] Project creation and display
- [ ] Developer application form
- [ ] Email functionality
- [ ] File upload capabilities
- [ ] Responsive design on mobile

## 7. Troubleshooting

### 7.1 Common Issues

#### Database Connection Error
```
Error: Could not connect to database
Solution: Check database credentials and server status
```

#### Permission Denied
```
Error: Permission denied writing to uploads directory
Solution: Set proper directory permissions (755 for directories, 644 for files)
```

#### PHP Extension Missing
```
Error: Required PHP extension not found
Solution: Install missing extensions or enable in php.ini
```

### 7.2 Debug Mode
```php
// Enable debug mode in config/config.php
define('DEBUG_MODE', true);
define('LOG_ERRORS', true);
```

### 7.3 Log Files
- **Error Log**: `storage/logs/error.log`
- **Access Log**: `storage/logs/access.log`
- **Installation Log**: `storage/logs/install.log`

## 8. Backup and Maintenance

### 8.1 Backup Procedures
```bash
# Database backup
mysqldump -u username -p south_safari > backup_$(date +%Y%m%d).sql

# File backup
tar -czf south_safari_files_$(date +%Y%m%d).tar.gz /path/to/south-safari/
```

### 8.2 Update Procedures
1. **Backup**: Create full backup before updating
2. **Download**: Get latest version
3. **Replace Files**: Replace application files (keep config)
4. **Run Migrations**: Execute database updates
5. **Test**: Verify functionality

## 9. Support and Resources

### 9.1 Documentation
- User Manual: `/docs/User_Manual.md`
- API Documentation: `/docs/API_Documentation.md`
- Developer Guide: `/docs/Developer_Guide.md`

### 9.2 Support Channels
- Installation issues: Check troubleshooting section
- Configuration help: Review documentation
- Custom modifications: Consult developer guide

---

**Document Version**: 1.0  
**Last Updated**: 2025-07-07  
**Status**: Draft
