<?php
/**
 * South Safari Platform - Root Redirect
 *
 * This file redirects requests from the root directory to the installer
 * or the public folder based on installation status.
 */

// Check if the application is installed
if (!file_exists('config/config.php') || filesize('config/config.php') < 100) {
    // Redirect to installer
    header('Location: install/installer.php');
    exit;
} else {
    // Redirect to public folder
    header('Location: public/');
    exit;
}
?>
