/**
 * South Safari Platform - Main Stylesheet
 * 
 * Custom styles for the South Safari partnership platform
 */

/* ===== ROOT VARIABLES ===== */
:root {
    --primary-color: #667eea;
    --primary-dark: #5a67d8;
    --secondary-color: #764ba2;
    --success-color: #48bb78;
    --warning-color: #ed8936;
    --danger-color: #f56565;
    --info-color: #4299e1;
    --light-color: #f7fafc;
    --dark-color: #2d3748;
    --gray-100: #f7fafc;
    --gray-200: #edf2f7;
    --gray-300: #e2e8f0;
    --gray-400: #cbd5e0;
    --gray-500: #a0aec0;
    --gray-600: #718096;
    --gray-700: #4a5568;
    --gray-800: #2d3748;
    --gray-900: #1a202c;
    --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --border-radius: 0.5rem;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --transition: all 0.3s ease;
}

/* ===== GLOBAL STYLES ===== */
body {
    font-family: var(--font-family-sans);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: #ffffff;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

.display-1, .display-2, .display-3, .display-4, .display-5, .display-6 {
    font-weight: 700;
}

.lead {
    font-size: 1.125rem;
    font-weight: 400;
}

/* ===== BUTTONS ===== */
.btn {
    font-weight: 500;
    border-radius: var(--border-radius);
    transition: var(--transition);
    border: none;
    padding: 0.75rem 1.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, #6b46c1 100%);
    transform: translateY(-1px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.125rem;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* ===== CARDS ===== */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: var(--gray-100);
    border-bottom: 1px solid var(--gray-200);
    font-weight: 600;
}

/* ===== NAVIGATION ===== */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: rgba(255, 255, 255, 0.8);
    transition: var(--transition);
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after {
    width: 80%;
}

/* ===== FORMS ===== */
.form-control {
    border: 2px solid var(--gray-300);
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-label {
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

.form-select {
    border: 2px solid var(--gray-300);
    border-radius: var(--border-radius);
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* ===== ALERTS ===== */
.alert {
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
}

.alert-success {
    background-color: #f0fff4;
    color: #22543d;
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background-color: #fff5f5;
    color: #742a2a;
    border-left: 4px solid var(--danger-color);
}

.alert-warning {
    background-color: #fffbeb;
    color: #744210;
    border-left: 4px solid var(--warning-color);
}

.alert-info {
    background-color: #ebf8ff;
    color: #2a4365;
    border-left: 4px solid var(--info-color);
}

/* ===== BADGES ===== */
.badge {
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius);
}

/* ===== PAGINATION ===== */
.pagination .page-link {
    border: none;
    color: var(--primary-color);
    font-weight: 500;
    margin: 0 0.25rem;
    border-radius: var(--border-radius);
}

.pagination .page-link:hover {
    background-color: var(--primary-color);
    color: white;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* ===== UTILITIES ===== */
.text-light-50 {
    color: rgba(255, 255, 255, 0.7) !important;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.shadow-soft {
    box-shadow: 0 2px 25px rgba(0, 0, 0, 0.1) !important;
}

.border-radius-lg {
    border-radius: 1rem !important;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .display-4 {
        font-size: 2rem;
    }
    
    .display-5 {
        font-size: 1.75rem;
    }
    
    .btn-lg {
        padding: 0.875rem 1.5rem;
        font-size: 1rem;
    }
    
    .hero-section {
        padding: 3rem 0 !important;
    }
    
    .stats-section .stat-number {
        font-size: 2rem !important;
    }
}

/* ===== CUSTOM COMPONENTS ===== */

/* Loading Spinner */
.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--gray-300);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Project Cards */
.project-card {
    transition: var(--transition);
    border: 1px solid var(--gray-200);
}

.project-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-color);
}

.project-meta {
    font-size: 0.875rem;
}

/* Category Cards */
.category-card {
    text-align: center;
    transition: var(--transition);
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.category-icon {
    margin-bottom: 1rem;
}

/* Stats Section */
.stats-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.stat-item {
    text-align: center;
}

.stat-number {
    color: var(--primary-color);
    font-weight: 700;
}

/* Footer */
footer {
    background: linear-gradient(135deg, var(--gray-800) 0%, var(--gray-900) 100%);
}

footer a:hover {
    color: var(--primary-color) !important;
    transition: var(--transition);
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    transition: var(--transition);
}

.social-links a:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
    :root {
        --gray-100: #2d3748;
        --gray-200: #4a5568;
        --gray-300: #718096;
    }
}

/* ===== PRINT STYLES ===== */
@media print {
    .navbar,
    .footer,
    .btn,
    .alert {
        display: none !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    .container {
        max-width: none !important;
    }
}
