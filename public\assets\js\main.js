/**
 * South Safari Platform - Main JavaScript
 * 
 * Core JavaScript functionality for the South Safari platform
 */

(function() {
    'use strict';

    // ===== GLOBAL VARIABLES =====
    const SouthSafari = {
        csrfToken: window.csrfToken || '',
        baseUrl: window.location.origin,
        currentUser: window.currentUser || null,
        debug: window.debug || false
    };

    // ===== UTILITY FUNCTIONS =====
    
    /**
     * Log messages in debug mode
     */
    function log(message, type = 'info') {
        if (SouthSafari.debug) {
            console[type]('[South Safari]', message);
        }
    }

    /**
     * Show notification
     */
    function showNotification(message, type = 'info', duration = 5000) {
        const alertClass = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'warning': 'alert-warning',
            'info': 'alert-info'
        }[type] || 'alert-info';

        const notification = document.createElement('div');
        notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto remove after duration
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, duration);
    }

    /**
     * Make AJAX request
     */
    function ajaxRequest(url, options = {}) {
        const defaults = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': SouthSafari.csrfToken
            }
        };

        const config = Object.assign({}, defaults, options);
        
        // Add CSRF token to form data if it's a POST request
        if (config.method === 'POST' && config.body instanceof FormData) {
            config.body.append('csrf_token', SouthSafari.csrfToken);
            delete config.headers['Content-Type']; // Let browser set it for FormData
        }

        return fetch(url, config)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .catch(error => {
                log('AJAX request failed: ' + error.message, 'error');
                throw error;
            });
    }

    /**
     * Format date
     */
    function formatDate(dateString, options = {}) {
        const date = new Date(dateString);
        const defaultOptions = {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        };
        
        return date.toLocaleDateString('en-US', Object.assign(defaultOptions, options));
    }

    /**
     * Truncate text
     */
    function truncateText(text, length = 100, suffix = '...') {
        if (text.length <= length) return text;
        return text.substring(0, length) + suffix;
    }

    /**
     * Debounce function
     */
    function debounce(func, wait, immediate) {
        let timeout;
        return function executedFunction() {
            const context = this;
            const args = arguments;
            const later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }

    // ===== FORM HANDLING =====
    
    /**
     * Handle form submissions with AJAX
     */
    function handleAjaxForms() {
        document.querySelectorAll('form[data-ajax="true"]').forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const formData = new FormData(this);
                const submitBtn = this.querySelector('button[type="submit"]');
                const originalText = submitBtn ? submitBtn.innerHTML : '';
                
                // Show loading state
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
                }

                ajaxRequest(this.action, {
                    method: this.method || 'POST',
                    body: formData
                })
                .then(response => {
                    if (response.status === 'success') {
                        showNotification(response.message, 'success');
                        
                        // Reset form if specified
                        if (this.dataset.reset === 'true') {
                            this.reset();
                        }
                        
                        // Redirect if specified
                        if (response.redirect) {
                            setTimeout(() => {
                                window.location.href = response.redirect;
                            }, 1000);
                        }
                    } else {
                        showNotification(response.message || 'An error occurred', 'error');
                    }
                })
                .catch(error => {
                    showNotification('An error occurred. Please try again.', 'error');
                })
                .finally(() => {
                    // Restore button state
                    if (submitBtn) {
                        submitBtn.disabled = false;
                        submitBtn.innerHTML = originalText;
                    }
                });
            });
        });
    }

    /**
     * Handle file uploads with preview
     */
    function handleFileUploads() {
        document.querySelectorAll('input[type="file"][data-preview]').forEach(input => {
            input.addEventListener('change', function() {
                const previewContainer = document.querySelector(this.dataset.preview);
                if (!previewContainer) return;

                previewContainer.innerHTML = '';

                Array.from(this.files).forEach(file => {
                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const img = document.createElement('img');
                            img.src = e.target.result;
                            img.className = 'img-thumbnail me-2 mb-2';
                            img.style.maxWidth = '100px';
                            img.style.maxHeight = '100px';
                            previewContainer.appendChild(img);
                        };
                        reader.readAsDataURL(file);
                    } else {
                        const fileInfo = document.createElement('div');
                        fileInfo.className = 'alert alert-info me-2 mb-2 d-inline-block';
                        fileInfo.innerHTML = `<i class="fas fa-file"></i> ${file.name}`;
                        previewContainer.appendChild(fileInfo);
                    }
                });
            });
        });
    }

    // ===== SEARCH FUNCTIONALITY =====
    
    /**
     * Handle live search
     */
    function handleLiveSearch() {
        const searchInputs = document.querySelectorAll('input[data-live-search]');
        
        searchInputs.forEach(input => {
            const searchUrl = input.dataset.liveSearch;
            const resultsContainer = document.querySelector(input.dataset.results);
            
            if (!resultsContainer) return;

            const debouncedSearch = debounce(function() {
                const query = input.value.trim();
                
                if (query.length < 2) {
                    resultsContainer.innerHTML = '';
                    return;
                }

                // Show loading
                resultsContainer.innerHTML = '<div class="text-center p-3"><i class="fas fa-spinner fa-spin"></i> Searching...</div>';

                ajaxRequest(`${searchUrl}?q=${encodeURIComponent(query)}`)
                    .then(response => {
                        if (response.status === 'success') {
                            resultsContainer.innerHTML = response.html || '<div class="text-center p-3 text-muted">No results found</div>';
                        } else {
                            resultsContainer.innerHTML = '<div class="text-center p-3 text-danger">Search failed</div>';
                        }
                    })
                    .catch(() => {
                        resultsContainer.innerHTML = '<div class="text-center p-3 text-danger">Search failed</div>';
                    });
            }, 300);

            input.addEventListener('input', debouncedSearch);
        });
    }

    // ===== UI ENHANCEMENTS =====
    
    /**
     * Handle tooltips
     */
    function initializeTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    /**
     * Handle popovers
     */
    function initializePopovers() {
        const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
    }

    /**
     * Handle smooth scrolling
     */
    function handleSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    e.preventDefault();
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    /**
     * Handle back to top button
     */
    function handleBackToTop() {
        const backToTopBtn = document.getElementById('backToTop');
        if (!backToTopBtn) return;

        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTopBtn.style.display = 'block';
            } else {
                backToTopBtn.style.display = 'none';
            }
        });

        backToTopBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    /**
     * Handle loading states
     */
    function handleLoadingStates() {
        document.querySelectorAll('[data-loading-text]').forEach(element => {
            element.addEventListener('click', function() {
                const originalText = this.innerHTML;
                const loadingText = this.dataset.loadingText;
                
                this.disabled = true;
                this.innerHTML = loadingText;
                
                // Restore after 3 seconds (fallback)
                setTimeout(() => {
                    this.disabled = false;
                    this.innerHTML = originalText;
                }, 3000);
            });
        });
    }

    // ===== INITIALIZATION =====
    
    /**
     * Initialize all functionality when DOM is ready
     */
    function initialize() {
        log('Initializing South Safari platform...');
        
        // Form handling
        handleAjaxForms();
        handleFileUploads();
        
        // Search functionality
        handleLiveSearch();
        
        // UI enhancements
        initializeTooltips();
        initializePopovers();
        handleSmoothScrolling();
        handleBackToTop();
        handleLoadingStates();
        
        log('South Safari platform initialized successfully');
    }

    // ===== EVENT LISTENERS =====
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }

    // Handle page visibility changes
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            log('Page hidden');
        } else {
            log('Page visible');
        }
    });

    // ===== GLOBAL EXPOSURE =====
    
    // Expose utilities globally
    window.SouthSafari = Object.assign(SouthSafari, {
        log,
        showNotification,
        ajaxRequest,
        formatDate,
        truncateText,
        debounce
    });

})();
