<?php
/**
 * South Safari Platform - Main Entry Point
 * 
 * This file serves as the main entry point for all requests to the South Safari platform.
 * It initializes the application and handles the request routing.
 */

// Define application constants
define('SOUTH_SAFARI', true);
define('START_TIME', microtime(true));
define('ROOT_PATH', dirname(__DIR__));

// Check if configuration file exists
if (!file_exists(ROOT_PATH . '/config/config.php')) {
    // Check if installer exists
    if (file_exists(ROOT_PATH . '/install/installer.php')) {
        header('Location: install/installer.php');
        exit;
    } else {
        die('Configuration file not found. Please run the installer or create config/config.php manually.');
    }
}

// Load configuration
require_once ROOT_PATH . '/config/config.php';

// Load autoloader
require_once ROOT_PATH . '/src/Core/Autoloader.php';

// Register autoloader
SouthSafari\Core\Autoloader::register();

// Handle maintenance mode
if (defined('FEATURE_MAINTENANCE_MODE') && FEATURE_MAINTENANCE_MODE) {
    // Allow admin access during maintenance
    session_start();
    if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
        require_once ROOT_PATH . '/src/Views/maintenance.php';
        exit;
    }
}

// Start output buffering
ob_start();

try {
    // Initialize and run the application
    $app = SouthSafari\Core\App::getInstance();
    $app->run();
    
} catch (Exception $e) {
    // Handle uncaught exceptions
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        echo "<h1>Application Error</h1>";
        echo "<p><strong>Message:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
        echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
        echo "<h3>Stack Trace:</h3>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    } else {
        // Log error and show generic error page
        error_log("Application Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
        
        http_response_code(500);
        require_once ROOT_PATH . '/src/Views/errors/500.php';
    }
} catch (Error $e) {
    // Handle fatal errors
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        echo "<h1>Fatal Error</h1>";
        echo "<p><strong>Message:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
        echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
        echo "<h3>Stack Trace:</h3>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    } else {
        error_log("Fatal Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
        
        http_response_code(500);
        echo "An unexpected error occurred. Please try again later.";
    }
}

// End output buffering and send response
ob_end_flush();

// Log execution time in debug mode
if (defined('DEBUG_MODE') && DEBUG_MODE) {
    $executionTime = microtime(true) - START_TIME;
    error_log("Page execution time: " . number_format($executionTime * 1000, 2) . "ms");
}

?>
