<?php
/**
 * Simple Test Script for South Safari Platform
 * Tests basic functionality without full installation
 */

echo "=== South Safari Platform - Simple Test ===\n\n";

// Test 1: PHP Version
echo "1. PHP Version Test:\n";
echo "   Current PHP Version: " . PHP_VERSION . "\n";
echo "   Required: 7.4+\n";
echo "   Status: " . (version_compare(PHP_VERSION, '7.4.0', '>=') ? "✓ PASS" : "✗ FAIL") . "\n\n";

// Test 2: Required Extensions
echo "2. PHP Extensions Test:\n";
$required_extensions = ['pdo', 'pdo_mysql', 'mbstring', 'openssl', 'curl', 'json'];
foreach ($required_extensions as $ext) {
    $loaded = extension_loaded($ext);
    echo "   {$ext}: " . ($loaded ? "✓ LOADED" : "✗ MISSING") . "\n";
}
echo "\n";

// Test 3: File Structure
echo "3. File Structure Test:\n";
$required_files = [
    'public/index.php',
    'src/Core/App.php',
    'src/Core/Database.php',
    'src/Core/Router.php',
    'src/Controllers/HomeController.php',
    'config/config.template.php',
    'database/schema.sql'
];

foreach ($required_files as $file) {
    $exists = file_exists($file);
    echo "   {$file}: " . ($exists ? "✓ EXISTS" : "✗ MISSING") . "\n";
}
echo "\n";

// Test 4: Directory Permissions
echo "4. Directory Permissions Test:\n";
$required_dirs = [
    'public/uploads' => true,
    'storage/cache' => true,
    'storage/logs' => true,
    'config' => true
];

foreach ($required_dirs as $dir => $should_be_writable) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
    $writable = is_writable($dir);
    echo "   {$dir}: " . ($writable ? "✓ WRITABLE" : "✗ NOT WRITABLE") . "\n";
}
echo "\n";

// Test 5: Autoloader Test
echo "5. Autoloader Test:\n";
try {
    require_once 'src/Core/Autoloader.php';
    SouthSafari\Core\Autoloader::register();
    echo "   Autoloader registration: ✓ SUCCESS\n";
    
    // Test class loading
    $testClasses = [
        'SouthSafari\Core\App',
        'SouthSafari\Core\Database',
        'SouthSafari\Core\Router',
        'SouthSafari\Controllers\HomeController'
    ];
    
    foreach ($testClasses as $class) {
        if (class_exists($class)) {
            echo "   {$class}: ✓ LOADABLE\n";
        } else {
            echo "   {$class}: ✗ FAILED TO LOAD\n";
        }
    }
} catch (Exception $e) {
    echo "   Autoloader test: ✗ FAILED - " . $e->getMessage() . "\n";
}
echo "\n";

// Test 6: Database Schema Test
echo "6. Database Schema Test:\n";
if (file_exists('database/schema.sql')) {
    $schema = file_get_contents('database/schema.sql');
    $tables = ['users', 'projects', 'applications', 'categories', 'messages'];
    foreach ($tables as $table) {
        $found = strpos($schema, "CREATE TABLE `ss_{$table}`") !== false;
        echo "   Table {$table}: " . ($found ? "✓ DEFINED" : "✗ MISSING") . "\n";
    }
} else {
    echo "   Schema file: ✗ MISSING\n";
}
echo "\n";

// Test 7: Configuration Template Test
echo "7. Configuration Template Test:\n";
if (file_exists('config/config.template.php')) {
    $config = file_get_contents('config/config.template.php');
    $required_constants = ['DB_HOST', 'DB_NAME', 'DB_USER', 'SECRET_KEY', 'APP_URL'];
    foreach ($required_constants as $constant) {
        $found = strpos($config, "define('{$constant}'") !== false;
        echo "   {$constant}: " . ($found ? "✓ DEFINED" : "✗ MISSING") . "\n";
    }
} else {
    echo "   Config template: ✗ MISSING\n";
}
echo "\n";

// Test 8: Asset Files Test
echo "8. Asset Files Test:\n";
$asset_files = [
    'public/assets/css/main.css',
    'public/assets/js/main.js',
    'public/.htaccess'
];

foreach ($asset_files as $file) {
    $exists = file_exists($file);
    echo "   {$file}: " . ($exists ? "✓ EXISTS" : "✗ MISSING") . "\n";
}
echo "\n";

// Summary
echo "=== TEST SUMMARY ===\n";
echo "Platform structure appears to be complete.\n";
echo "Next steps:\n";
echo "1. Set up a web server (Apache/Nginx) or use PHP built-in server\n";
echo "2. Create a MySQL database\n";
echo "3. Run the installer at /install/installer.php\n";
echo "4. Test the full installation with test_installation.php\n\n";

echo "To start PHP built-in server:\n";
echo "php -S localhost:8080 -t public\n\n";

echo "Test completed at: " . date('Y-m-d H:i:s') . "\n";
?>
