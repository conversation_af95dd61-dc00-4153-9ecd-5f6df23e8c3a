<?php

namespace SouthSafari\Controllers;

/**
 * Authentication Controller
 * 
 * Handles user authentication, registration, and password management
 */
class AuthController extends BaseController
{
    /**
     * Show login form
     */
    public function showLogin($request, $response, $params = [])
    {
        $this->setRequestResponse($request, $response);
        
        // Redirect if already logged in
        if ($this->isLoggedIn()) {
            return $this->redirect($this->isAdmin() ? '/admin' : '/dashboard');
        }
        
        $content = $this->render('auth/login', [
            'layout' => 'auth',
            'title' => 'Login - South Safari',
            'page_class' => 'login-page'
        ]);
        
        return $response->html($content);
    }

    /**
     * Handle login
     */
    public function login($request, $response, $params = [])
    {
        $this->setRequestResponse($request, $response);
        
        // Validate CSRF token
        if (!$this->validateCsrfToken()) {
            $this->setFlashMessage('error', 'Security token validation failed.');
            return $this->redirect('/login');
        }
        
        $email = $this->sanitize($request->post('email'), 'email');
        $password = $request->post('password');
        $remember = $request->post('remember') === '1';
        
        // Validate input
        $errors = $this->validate([
            'email' => $email,
            'password' => $password
        ], [
            'email' => 'required|email',
            'password' => 'required'
        ]);
        
        if (!empty($errors)) {
            $this->setFlashMessage('error', 'Please provide valid email and password.');
            return $this->redirect('/login');
        }
        
        // Attempt login
        $user = $this->authenticateUser($email, $password);
        
        if (!$user) {
            $this->setFlashMessage('error', 'Invalid email or password.');
            $this->logActivity('login_failed', "Failed login attempt for email: {$email}");
            return $this->redirect('/login');
        }
        
        // Check if account is active
        if ($user['status'] !== 'active') {
            $this->setFlashMessage('error', 'Your account is not active. Please contact support.');
            return $this->redirect('/login');
        }
        
        // Log the user in
        $this->loginUser($user, $remember);
        
        // Update last login
        $this->updateLastLogin($user['id']);
        
        // Log successful login
        $this->logActivity('login_success', "User logged in: {$user['email']}");
        
        // Redirect to intended URL or dashboard
        $intendedUrl = $this->session->get('intended_url');
        $this->session->remove('intended_url');
        
        $redirectUrl = $intendedUrl ?: ($user['role'] === 'admin' ? '/admin' : '/dashboard');
        
        return $this->redirect($redirectUrl);
    }

    /**
     * Show registration form
     */
    public function showRegister($request, $response, $params = [])
    {
        $this->setRequestResponse($request, $response);
        
        // Check if registration is enabled
        if (!defined('FEATURE_REGISTRATION') || !FEATURE_REGISTRATION) {
            $this->setFlashMessage('error', 'Registration is currently disabled.');
            return $this->redirect('/');
        }
        
        // Redirect if already logged in
        if ($this->isLoggedIn()) {
            return $this->redirect($this->isAdmin() ? '/admin' : '/dashboard');
        }
        
        $content = $this->render('auth/register', [
            'layout' => 'auth',
            'title' => 'Register - South Safari',
            'page_class' => 'register-page'
        ]);
        
        return $response->html($content);
    }

    /**
     * Handle registration
     */
    public function register($request, $response, $params = [])
    {
        $this->setRequestResponse($request, $response);
        
        // Check if registration is enabled
        if (!defined('FEATURE_REGISTRATION') || !FEATURE_REGISTRATION) {
            return $this->error('Registration is currently disabled.', null, 403);
        }
        
        // Validate CSRF token
        if (!$this->validateCsrfToken()) {
            $this->setFlashMessage('error', 'Security token validation failed.');
            return $this->redirect('/register');
        }
        
        $data = [
            'first_name' => $this->sanitize($request->post('first_name')),
            'last_name' => $this->sanitize($request->post('last_name')),
            'email' => $this->sanitize($request->post('email'), 'email'),
            'username' => $this->sanitize($request->post('username')),
            'password' => $request->post('password'),
            'password_confirm' => $request->post('password_confirm'),
            'country' => $this->sanitize($request->post('country')),
            'company_name' => $this->sanitize($request->post('company_name'))
        ];
        
        // Validate input
        $errors = $this->validateRegistration($data);
        
        if (!empty($errors)) {
            $this->setFlashMessage('error', 'Please correct the errors below.');
            // In a real app, you'd pass errors back to the form
            return $this->redirect('/register');
        }
        
        try {
            // Create user account
            $userId = $this->createUser($data);
            
            // Log registration
            $this->logActivity('user_registered', "New user registered: {$data['email']}");
            
            $this->setFlashMessage('success', 'Registration successful! Please check your email to verify your account.');
            
            // Send verification email if enabled
            if (defined('FEATURE_EMAIL_VERIFICATION') && FEATURE_EMAIL_VERIFICATION) {
                $this->sendVerificationEmail($userId, $data['email']);
            } else {
                // Auto-activate if email verification is disabled
                $this->activateUser($userId);
                $this->setFlashMessage('success', 'Registration successful! You can now log in.');
            }
            
            return $this->redirect('/login');
            
        } catch (\Exception $e) {
            error_log("Registration error: " . $e->getMessage());
            $this->setFlashMessage('error', 'Registration failed. Please try again.');
            return $this->redirect('/register');
        }
    }

    /**
     * Handle logout
     */
    public function logout($request, $response, $params = [])
    {
        $this->setRequestResponse($request, $response);
        
        // Log logout
        if ($this->isLoggedIn()) {
            $user = $this->getCurrentUser();
            $this->logActivity('logout', "User logged out: {$user['email']}");
        }
        
        // Clear session
        $this->session->destroy();
        
        $this->setFlashMessage('success', 'You have been logged out successfully.');
        return $this->redirect('/');
    }

    /**
     * Show forgot password form
     */
    public function showForgotPassword($request, $response, $params = [])
    {
        $this->setRequestResponse($request, $response);
        
        $content = $this->render('auth/forgot-password', [
            'layout' => 'auth',
            'title' => 'Forgot Password - South Safari',
            'page_class' => 'forgot-password-page'
        ]);
        
        return $response->html($content);
    }

    /**
     * Handle forgot password
     */
    public function forgotPassword($request, $response, $params = [])
    {
        $this->setRequestResponse($request, $response);
        
        // Validate CSRF token
        if (!$this->validateCsrfToken()) {
            $this->setFlashMessage('error', 'Security token validation failed.');
            return $this->redirect('/forgot-password');
        }
        
        $email = $this->sanitize($request->post('email'), 'email');
        
        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $this->setFlashMessage('error', 'Please provide a valid email address.');
            return $this->redirect('/forgot-password');
        }
        
        try {
            // Generate reset token
            $token = $this->generatePasswordResetToken($email);
            
            if ($token) {
                // Send reset email
                $this->sendPasswordResetEmail($email, $token);
                $this->logActivity('password_reset_requested', "Password reset requested for: {$email}");
            }
            
            // Always show success message for security
            $this->setFlashMessage('success', 'If an account with that email exists, you will receive password reset instructions.');
            
        } catch (\Exception $e) {
            error_log("Password reset error: " . $e->getMessage());
            $this->setFlashMessage('error', 'An error occurred. Please try again.');
        }
        
        return $this->redirect('/forgot-password');
    }

    /**
     * Authenticate user credentials
     */
    private function authenticateUser($email, $password)
    {
        $user = $this->db->selectOne(
            "SELECT * FROM " . $this->db->table('users') . " WHERE email = ? AND deleted_at IS NULL",
            [$email]
        );
        
        if ($user && password_verify($password, $user['password_hash'])) {
            return $user;
        }
        
        return false;
    }

    /**
     * Log user in
     */
    private function loginUser($user, $remember = false)
    {
        $this->session->set('user_id', $user['id']);
        $this->session->set('user_role', $user['role']);
        $this->session->set('user_email', $user['email']);
        $this->session->set('user_name', $user['first_name'] . ' ' . $user['last_name']);
        
        // Regenerate session ID for security
        $this->session->regenerateId();
        
        // Handle remember me functionality
        if ($remember) {
            // Set longer session lifetime
            ini_set('session.gc_maxlifetime', 30 * 24 * 60 * 60); // 30 days
        }
    }

    /**
     * Update last login timestamp
     */
    private function updateLastLogin($userId)
    {
        $this->db->update('users', 
            ['last_login' => date('Y-m-d H:i:s')], 
            'id = ?', 
            [$userId]
        );
    }

    /**
     * Validate registration data
     */
    private function validateRegistration($data)
    {
        $errors = $this->validate($data, [
            'first_name' => 'required|max:100',
            'last_name' => 'required|max:100',
            'email' => 'required|email|max:255',
            'username' => 'required|min:3|max:50',
            'password' => 'required|min:8',
            'country' => 'required|max:100'
        ]);
        
        // Check password confirmation
        if ($data['password'] !== $data['password_confirm']) {
            $errors['password_confirm'] = 'Passwords do not match.';
        }
        
        // Check if email already exists
        if ($this->db->exists('users', 'email = ?', [$data['email']])) {
            $errors['email'] = 'Email address is already registered.';
        }
        
        // Check if username already exists
        if ($this->db->exists('users', 'username = ?', [$data['username']])) {
            $errors['username'] = 'Username is already taken.';
        }
        
        return $errors;
    }

    /**
     * Create new user account
     */
    private function createUser($data)
    {
        $userData = [
            'username' => $data['username'],
            'email' => $data['email'],
            'password_hash' => password_hash($data['password'], PASSWORD_DEFAULT),
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'country' => $data['country'],
            'company_name' => $data['company_name'],
            'role' => 'developer',
            'status' => defined('FEATURE_EMAIL_VERIFICATION') && FEATURE_EMAIL_VERIFICATION ? 'pending' : 'active',
            'email_verification_token' => bin2hex(random_bytes(32))
        ];
        
        return $this->db->insert('users', $userData);
    }

    /**
     * Generate password reset token
     */
    private function generatePasswordResetToken($email)
    {
        $user = $this->db->selectOne(
            "SELECT id FROM " . $this->db->table('users') . " WHERE email = ? AND deleted_at IS NULL",
            [$email]
        );
        
        if (!$user) {
            return false;
        }
        
        $token = bin2hex(random_bytes(32));
        $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));
        
        $this->db->update('users', [
            'password_reset_token' => $token,
            'password_reset_expires' => $expires
        ], 'id = ?', [$user['id']]);
        
        return $token;
    }

    /**
     * Send verification email
     */
    private function sendVerificationEmail($userId, $email)
    {
        // TODO: Implement email sending
        error_log("Verification email should be sent to: {$email}");
    }

    /**
     * Send password reset email
     */
    private function sendPasswordResetEmail($email, $token)
    {
        // TODO: Implement email sending
        error_log("Password reset email should be sent to: {$email} with token: {$token}");
    }

    /**
     * Activate user account
     */
    private function activateUser($userId)
    {
        $this->db->update('users', [
            'status' => 'active',
            'email_verified' => 1,
            'email_verification_token' => null
        ], 'id = ?', [$userId]);
    }
}

?>
