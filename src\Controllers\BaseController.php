<?php

namespace SouthSafari\Controllers;

use SouthSafari\Core\Database;
use SouthSafari\Core\View;
use SouthSafari\Core\Session;

/**
 * Base Controller Class
 * 
 * Provides common functionality for all controllers
 */
abstract class BaseController
{
    protected $db;
    protected $view;
    protected $session;
    protected $request;
    protected $response;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->db = Database::getInstance();
        $this->view = new View();
        $this->session = new Session();
    }

    /**
     * Set request and response objects
     */
    public function setRequestResponse($request, $response)
    {
        $this->request = $request;
        $this->response = $response;
    }

    /**
     * Render a view
     */
    protected function render($template, $data = [])
    {
        // Add common data available to all views
        $data = array_merge($data, [
            'app_name' => APP_NAME ?? 'South Safari',
            'app_url' => APP_URL ?? '',
            'current_user' => $this->getCurrentUser(),
            'is_logged_in' => $this->isLoggedIn(),
            'is_admin' => $this->isAdmin(),
            'csrf_token' => $this->generateCsrfToken(),
            'flash_messages' => $this->getFlashMessages()
        ]);

        return $this->view->render($template, $data);
    }

    /**
     * Redirect to URL
     */
    protected function redirect($url, $statusCode = 302)
    {
        if ($this->response) {
            return $this->response->redirect($url, $statusCode);
        }
        
        header("Location: $url", true, $statusCode);
        exit;
    }

    /**
     * Return JSON response
     */
    protected function json($data, $statusCode = 200)
    {
        if ($this->response) {
            return $this->response->json($data, $statusCode);
        }
        
        header('Content-Type: application/json');
        http_response_code($statusCode);
        echo json_encode($data);
        exit;
    }

    /**
     * Return success JSON response
     */
    protected function success($message = 'Success', $data = null, $statusCode = 200)
    {
        if ($this->response) {
            return $this->response->success($message, $data, $statusCode);
        }
        
        return $this->json([
            'status' => 'success',
            'message' => $message,
            'data' => $data
        ], $statusCode);
    }

    /**
     * Return error JSON response
     */
    protected function error($message = 'Error', $errors = null, $statusCode = 400)
    {
        if ($this->response) {
            return $this->response->error($message, $errors, $statusCode);
        }
        
        return $this->json([
            'status' => 'error',
            'message' => $message,
            'errors' => $errors
        ], $statusCode);
    }

    /**
     * Check if user is logged in
     */
    protected function isLoggedIn()
    {
        return $this->session->has('user_id');
    }

    /**
     * Check if user is admin
     */
    protected function isAdmin()
    {
        return $this->session->get('user_role') === 'admin';
    }

    /**
     * Get current user
     */
    protected function getCurrentUser()
    {
        if (!$this->isLoggedIn()) {
            return null;
        }

        $userId = $this->session->get('user_id');
        return $this->db->selectOne(
            "SELECT * FROM " . $this->db->table('users') . " WHERE id = ? AND deleted_at IS NULL",
            [$userId]
        );
    }

    /**
     * Require authentication
     */
    protected function requireAuth()
    {
        if (!$this->isLoggedIn()) {
            if ($this->isAjaxRequest()) {
                return $this->error('Authentication required', null, 401);
            }
            
            $this->setFlashMessage('error', 'Please log in to access this page.');
            return $this->redirect('/login');
        }
    }

    /**
     * Require admin privileges
     */
    protected function requireAdmin()
    {
        $this->requireAuth();
        
        if (!$this->isAdmin()) {
            if ($this->isAjaxRequest()) {
                return $this->error('Admin privileges required', null, 403);
            }
            
            $this->setFlashMessage('error', 'Access denied. Admin privileges required.');
            return $this->redirect('/');
        }
    }

    /**
     * Check if request is AJAX
     */
    protected function isAjaxRequest()
    {
        if ($this->request) {
            return $this->request->isAjax();
        }
        
        return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }

    /**
     * Validate CSRF token
     */
    protected function validateCsrfToken($token = null)
    {
        if ($token === null) {
            $token = $this->request ? $this->request->post('csrf_token') : ($_POST['csrf_token'] ?? '');
        }
        
        $sessionToken = $this->session->get('csrf_token');
        return $token && $sessionToken && hash_equals($sessionToken, $token);
    }

    /**
     * Generate CSRF token
     */
    protected function generateCsrfToken()
    {
        if (!$this->session->has('csrf_token')) {
            $this->session->set('csrf_token', bin2hex(random_bytes(32)));
        }
        
        return $this->session->get('csrf_token');
    }

    /**
     * Set flash message
     */
    protected function setFlashMessage($type, $message)
    {
        $messages = $this->session->get('flash_messages', []);
        $messages[] = ['type' => $type, 'message' => $message];
        $this->session->set('flash_messages', $messages);
    }

    /**
     * Get and clear flash messages
     */
    protected function getFlashMessages()
    {
        $messages = $this->session->get('flash_messages', []);
        $this->session->remove('flash_messages');
        return $messages;
    }

    /**
     * Validate input data
     */
    protected function validate($data, $rules)
    {
        $errors = [];
        
        foreach ($rules as $field => $rule) {
            $value = $data[$field] ?? null;
            $ruleList = is_string($rule) ? explode('|', $rule) : $rule;
            
            foreach ($ruleList as $singleRule) {
                $error = $this->validateField($field, $value, $singleRule);
                if ($error) {
                    $errors[$field] = $error;
                    break; // Stop at first error for this field
                }
            }
        }
        
        return $errors;
    }

    /**
     * Validate single field
     */
    private function validateField($field, $value, $rule)
    {
        $parts = explode(':', $rule);
        $ruleName = $parts[0];
        $ruleValue = $parts[1] ?? null;
        
        switch ($ruleName) {
            case 'required':
                if (empty($value)) {
                    return ucfirst($field) . ' is required.';
                }
                break;
                
            case 'email':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    return ucfirst($field) . ' must be a valid email address.';
                }
                break;
                
            case 'min':
                if (!empty($value) && strlen($value) < $ruleValue) {
                    return ucfirst($field) . " must be at least {$ruleValue} characters.";
                }
                break;
                
            case 'max':
                if (!empty($value) && strlen($value) > $ruleValue) {
                    return ucfirst($field) . " must not exceed {$ruleValue} characters.";
                }
                break;
                
            case 'unique':
                if (!empty($value)) {
                    $table = $this->db->table($ruleValue);
                    $exists = $this->db->exists($ruleValue, "{$field} = ?", [$value]);
                    if ($exists) {
                        return ucfirst($field) . ' already exists.';
                    }
                }
                break;
        }
        
        return null;
    }

    /**
     * Sanitize input
     */
    protected function sanitize($input, $type = 'string')
    {
        switch ($type) {
            case 'string':
                return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
            case 'email':
                return filter_var(trim($input), FILTER_SANITIZE_EMAIL);
            case 'url':
                return filter_var(trim($input), FILTER_SANITIZE_URL);
            case 'int':
                return (int) $input;
            case 'float':
                return (float) $input;
            default:
                return trim($input);
        }
    }

    /**
     * Paginate results
     */
    protected function paginate($query, $params = [], $perPage = null)
    {
        $perPage = $perPage ?? (defined('ITEMS_PER_PAGE') ? ITEMS_PER_PAGE : 20);
        $page = max(1, (int) ($this->request ? $this->request->get('page', 1) : ($_GET['page'] ?? 1)));
        $offset = ($page - 1) * $perPage;
        
        // Get total count
        $countQuery = preg_replace('/SELECT .+ FROM/i', 'SELECT COUNT(*) as total FROM', $query);
        $total = $this->db->selectOne($countQuery, $params)['total'];
        
        // Get paginated results
        $query .= " LIMIT {$perPage} OFFSET {$offset}";
        $items = $this->db->select($query, $params);
        
        return [
            'items' => $items,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'total_pages' => ceil($total / $perPage),
                'has_prev' => $page > 1,
                'has_next' => $page < ceil($total / $perPage)
            ]
        ];
    }

    /**
     * Log activity
     */
    protected function logActivity($action, $description = null, $relatedTable = null, $relatedId = null)
    {
        $userId = $this->isLoggedIn() ? $this->session->get('user_id') : null;
        $ip = $this->request ? $this->request->ip() : ($_SERVER['REMOTE_ADDR'] ?? '127.0.0.1');
        $userAgent = $this->request ? $this->request->userAgent() : ($_SERVER['HTTP_USER_AGENT'] ?? '');
        
        $this->db->insert('activity_logs', [
            'user_id' => $userId,
            'action' => $action,
            'description' => $description,
            'ip_address' => $ip,
            'user_agent' => $userAgent,
            'related_table' => $relatedTable,
            'related_id' => $relatedId
        ]);
    }
}

?>
