<?php

namespace SouthSafari\Controllers;

/**
 * Dashboard Controller
 * 
 * Handles user dashboard functionality
 */
class DashboardController extends BaseController
{
    /**
     * Display user dashboard
     */
    public function index($request, $response, $params = [])
    {
        $this->setRequestResponse($request, $response);
        $this->requireAuth();
        
        $user = $this->getCurrentUser();
        
        try {
            // Get user statistics
            $stats = $this->getUserStats($user['id']);
            
            // Get recent applications
            $recentApplications = $this->getRecentApplications($user['id']);
            
            // Get recommended projects
            $recommendedProjects = $this->getRecommendedProjects($user['id']);
            
            // Get recent messages
            $recentMessages = $this->getRecentMessages($user['id']);
            
            $content = $this->render('dashboard/index', [
                'layout' => 'main',
                'title' => 'Dashboard - South Safari',
                'meta_description' => 'Your South Safari dashboard - track applications, partnerships, and opportunities.',
                'user' => $user,
                'stats' => $stats,
                'recent_applications' => $recentApplications,
                'recommended_projects' => $recommendedProjects,
                'recent_messages' => $recentMessages,
                'page_class' => 'dashboard-page'
            ]);
            
            return $response->html($content);
            
        } catch (\Exception $e) {
            error_log("Dashboard error: " . $e->getMessage());
            return $response->error500();
        }
    }

    /**
     * Display user profile
     */
    public function profile($request, $response, $params = [])
    {
        $this->setRequestResponse($request, $response);
        $this->requireAuth();
        
        $user = $this->getCurrentUser();
        
        if ($request->getMethod() === 'POST') {
            return $this->updateProfile($request, $response);
        }
        
        $content = $this->render('dashboard/profile', [
            'layout' => 'main',
            'title' => 'My Profile - South Safari',
            'user' => $user,
            'profile_completion' => $this->calculateProfileCompletion($user),
            'page_class' => 'profile-page'
        ]);
        
        return $response->html($content);
    }

    /**
     * Display user applications
     */
    public function applications($request, $response, $params = [])
    {
        $this->setRequestResponse($request, $response);
        $this->requireAuth();
        
        $user = $this->getCurrentUser();
        
        try {
            // Get user applications with pagination
            $query = "
                SELECT a.*, p.title as project_title, p.slug as project_slug, 
                       p.category, u.first_name, u.last_name, u.company_name
                FROM " . $this->db->table('applications') . " a
                LEFT JOIN " . $this->db->table('projects') . " p ON a.project_id = p.id
                LEFT JOIN " . $this->db->table('users') . " u ON p.created_by = u.id
                WHERE a.user_id = ?
                ORDER BY a.created_at DESC
            ";
            
            $result = $this->paginate($query, [$user['id']]);
            
            $content = $this->render('dashboard/applications', [
                'layout' => 'main',
                'title' => 'My Applications - South Safari',
                'applications' => $result['items'],
                'pagination' => $result['pagination'],
                'page_class' => 'applications-page'
            ]);
            
            return $response->html($content);
            
        } catch (\Exception $e) {
            error_log("Applications page error: " . $e->getMessage());
            return $response->error500();
        }
    }

    /**
     * Display user messages
     */
    public function messages($request, $response, $params = [])
    {
        $this->setRequestResponse($request, $response);
        $this->requireAuth();
        
        $user = $this->getCurrentUser();
        
        try {
            // Get user messages with pagination
            $query = "
                SELECT m.*, u.first_name, u.last_name, u.company_name
                FROM " . $this->db->table('messages') . " m
                LEFT JOIN " . $this->db->table('users') . " u ON m.sender_id = u.id
                WHERE m.recipient_id = ? AND m.deleted_at IS NULL
                ORDER BY m.created_at DESC
            ";
            
            $result = $this->paginate($query, [$user['id']], 15);
            
            $content = $this->render('dashboard/messages', [
                'layout' => 'main',
                'title' => 'Messages - South Safari',
                'messages' => $result['items'],
                'pagination' => $result['pagination'],
                'page_class' => 'messages-page'
            ]);
            
            return $response->html($content);
            
        } catch (\Exception $e) {
            error_log("Messages page error: " . $e->getMessage());
            return $response->error500();
        }
    }

    /**
     * Get user statistics
     */
    private function getUserStats($userId)
    {
        try {
            $stats = [];
            
            // Total applications
            $stats['total_applications'] = $this->db->count('applications', 'user_id = ?', [$userId]);
            
            // Pending applications
            $stats['pending_applications'] = $this->db->count('applications', 
                'user_id = ? AND status = ?', [$userId, 'pending']);
            
            // Active partnerships
            $stats['active_partnerships'] = $this->db->count('partnerships', 
                'developer_id = ? AND status = ?', [$userId, 'active']);
            
            // Unread messages
            $stats['unread_messages'] = $this->db->count('messages', 
                'recipient_id = ? AND is_read = 0 AND deleted_at IS NULL', [$userId]);
            
            return $stats;
            
        } catch (\Exception $e) {
            error_log("Error getting user stats: " . $e->getMessage());
            return [
                'total_applications' => 0,
                'pending_applications' => 0,
                'active_partnerships' => 0,
                'unread_messages' => 0
            ];
        }
    }

    /**
     * Get recent applications
     */
    private function getRecentApplications($userId, $limit = 5)
    {
        try {
            return $this->db->select("
                SELECT a.*, p.title as project_title, p.slug as project_slug
                FROM " . $this->db->table('applications') . " a
                LEFT JOIN " . $this->db->table('projects') . " p ON a.project_id = p.id
                WHERE a.user_id = ?
                ORDER BY a.created_at DESC
                LIMIT ?
            ", [$userId, $limit]);
            
        } catch (\Exception $e) {
            error_log("Error getting recent applications: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get recommended projects
     */
    private function getRecommendedProjects($userId, $limit = 3)
    {
        try {
            // Simple recommendation: get latest projects user hasn't applied to
            return $this->db->select("
                SELECT p.*, u.first_name, u.last_name, u.company_name
                FROM " . $this->db->table('projects') . " p
                LEFT JOIN " . $this->db->table('users') . " u ON p.created_by = u.id
                WHERE p.status = 'published' 
                AND p.deleted_at IS NULL
                AND p.id NOT IN (
                    SELECT project_id FROM " . $this->db->table('applications') . " 
                    WHERE user_id = ?
                )
                ORDER BY p.created_at DESC
                LIMIT ?
            ", [$userId, $limit]);
            
        } catch (\Exception $e) {
            error_log("Error getting recommended projects: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get recent messages
     */
    private function getRecentMessages($userId, $limit = 5)
    {
        try {
            return $this->db->select("
                SELECT m.*, u.first_name, u.last_name
                FROM " . $this->db->table('messages') . " m
                LEFT JOIN " . $this->db->table('users') . " u ON m.sender_id = u.id
                WHERE m.recipient_id = ? AND m.deleted_at IS NULL
                ORDER BY m.created_at DESC
                LIMIT ?
            ", [$userId, $limit]);
            
        } catch (\Exception $e) {
            error_log("Error getting recent messages: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Calculate profile completion percentage
     */
    private function calculateProfileCompletion($user)
    {
        $fields = [
            'first_name', 'last_name', 'email', 'country', 
            'bio', 'company_name', 'website', 'phone'
        ];
        
        $completed = 0;
        foreach ($fields as $field) {
            if (!empty($user[$field])) {
                $completed++;
            }
        }
        
        return round(($completed / count($fields)) * 100);
    }

    /**
     * Update user profile
     */
    private function updateProfile($request, $response)
    {
        // Validate CSRF token
        if (!$this->validateCsrfToken()) {
            $this->setFlashMessage('error', 'Security token validation failed.');
            return $this->redirect('/dashboard/profile');
        }
        
        $user = $this->getCurrentUser();
        
        $data = [
            'first_name' => $this->sanitize($request->post('first_name')),
            'last_name' => $this->sanitize($request->post('last_name')),
            'country' => $this->sanitize($request->post('country')),
            'phone' => $this->sanitize($request->post('phone')),
            'company_name' => $this->sanitize($request->post('company_name')),
            'website' => $this->sanitize($request->post('website'), 'url'),
            'bio' => $this->sanitize($request->post('bio'))
        ];
        
        // Validate input
        $errors = $this->validate($data, [
            'first_name' => 'required|max:100',
            'last_name' => 'required|max:100',
            'country' => 'required|max:100'
        ]);
        
        if (!empty($errors)) {
            $this->setFlashMessage('error', 'Please correct the errors in the form.');
            return $this->redirect('/dashboard/profile');
        }
        
        try {
            // Update user profile
            $this->db->update('users', $data, 'id = ?', [$user['id']]);
            
            // Log activity
            $this->logActivity('profile_updated', 'User updated their profile');
            
            $this->setFlashMessage('success', 'Profile updated successfully!');
            
        } catch (\Exception $e) {
            error_log("Profile update error: " . $e->getMessage());
            $this->setFlashMessage('error', 'Failed to update profile. Please try again.');
        }
        
        return $this->redirect('/dashboard/profile');
    }
}

?>
