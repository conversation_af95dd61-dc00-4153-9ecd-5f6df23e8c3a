<?php

namespace SouthSafari\Controllers;

/**
 * Home Controller
 * 
 * Handles the main homepage and public pages
 */
class HomeController extends BaseController
{
    /**
     * Display the homepage
     */
    public function index($request, $response, $params = [])
    {
        $this->setRequestResponse($request, $response);
        
        try {
            // Get featured projects
            $featuredProjects = $this->getFeaturedProjects();
            
            // Get project categories
            $categories = $this->getCategories();
            
            // Get platform statistics
            $stats = $this->getPlatformStats();
            
            // Render homepage
            $content = $this->render('home/index', [
                'layout' => 'main',
                'title' => 'Welcome to South Safari - Partnership Platform',
                'meta_description' => 'Connect with South African opportunities. Join our platform to explore partnership opportunities and grow your business in the Southern African market.',
                'featured_projects' => $featuredProjects,
                'categories' => $categories,
                'stats' => $stats,
                'page_class' => 'homepage'
            ]);
            
            return $response->html($content);
            
        } catch (\Exception $e) {
            error_log("Homepage error: " . $e->getMessage());
            
            // Fallback content for homepage
            $content = $this->render('home/index', [
                'layout' => 'main',
                'title' => 'Welcome to South Safari',
                'featured_projects' => [],
                'categories' => [],
                'stats' => ['projects' => 0, 'developers' => 0, 'partnerships' => 0],
                'page_class' => 'homepage'
            ]);
            
            return $response->html($content);
        }
    }

    /**
     * Get featured projects for homepage
     */
    private function getFeaturedProjects()
    {
        try {
            $limit = defined('FEATURED_PROJECTS_LIMIT') ? FEATURED_PROJECTS_LIMIT : 6;
            
            $query = "
                SELECT p.*, u.first_name, u.last_name, u.company_name
                FROM " . $this->db->table('projects') . " p
                LEFT JOIN " . $this->db->table('users') . " u ON p.created_by = u.id
                WHERE p.status = 'published' 
                AND p.featured = 1 
                AND p.deleted_at IS NULL
                ORDER BY p.created_at DESC
                LIMIT ?
            ";
            
            return $this->db->select($query, [$limit]);
            
        } catch (\Exception $e) {
            error_log("Error fetching featured projects: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get project categories
     */
    private function getCategories()
    {
        try {
            $query = "
                SELECT c.*, COUNT(p.id) as project_count
                FROM " . $this->db->table('categories') . " c
                LEFT JOIN " . $this->db->table('projects') . " p ON c.slug = p.category AND p.status = 'published' AND p.deleted_at IS NULL
                WHERE c.is_active = 1
                GROUP BY c.id
                ORDER BY c.sort_order ASC, c.name ASC
            ";
            
            return $this->db->select($query);
            
        } catch (\Exception $e) {
            error_log("Error fetching categories: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get platform statistics
     */
    private function getPlatformStats()
    {
        try {
            $stats = [];
            
            // Count published projects
            $stats['projects'] = $this->db->count('projects', 'status = ? AND deleted_at IS NULL', ['published']);
            
            // Count active developers
            $stats['developers'] = $this->db->count('users', 'role = ? AND status = ? AND deleted_at IS NULL', ['developer', 'active']);
            
            // Count active partnerships
            $stats['partnerships'] = $this->db->count('partnerships', 'status IN (?, ?)', ['active', 'completed']);
            
            // Count total applications
            $stats['applications'] = $this->db->count('applications');
            
            return $stats;
            
        } catch (\Exception $e) {
            error_log("Error fetching platform stats: " . $e->getMessage());
            return [
                'projects' => 0,
                'developers' => 0,
                'partnerships' => 0,
                'applications' => 0
            ];
        }
    }

    /**
     * Display about page
     */
    public function about($request, $response, $params = [])
    {
        $this->setRequestResponse($request, $response);
        
        $content = $this->render('home/about', [
            'layout' => 'main',
            'title' => 'About South Safari - Partnership Platform',
            'meta_description' => 'Learn about South Safari platform and how we connect South African opportunities with global talent.',
            'page_class' => 'about-page'
        ]);
        
        return $response->html($content);
    }

    /**
     * Display how it works page
     */
    public function howItWorks($request, $response, $params = [])
    {
        $this->setRequestResponse($request, $response);
        
        $content = $this->render('home/how-it-works', [
            'layout' => 'main',
            'title' => 'How It Works - South Safari Platform',
            'meta_description' => 'Learn how to get started with South Safari platform and find partnership opportunities.',
            'page_class' => 'how-it-works-page'
        ]);
        
        return $response->html($content);
    }

    /**
     * Display contact page
     */
    public function contact($request, $response, $params = [])
    {
        $this->setRequestResponse($request, $response);
        
        if ($request->getMethod() === 'POST') {
            return $this->handleContactForm($request, $response);
        }
        
        $content = $this->render('home/contact', [
            'layout' => 'main',
            'title' => 'Contact Us - South Safari Platform',
            'meta_description' => 'Get in touch with the South Safari team for support or partnership inquiries.',
            'page_class' => 'contact-page'
        ]);
        
        return $response->html($content);
    }

    /**
     * Handle contact form submission
     */
    private function handleContactForm($request, $response)
    {
        // Validate CSRF token
        if (!$this->validateCsrfToken()) {
            $this->setFlashMessage('error', 'Security token validation failed. Please try again.');
            return $this->redirect('/contact');
        }
        
        // Get form data
        $data = [
            'name' => $this->sanitize($request->post('name')),
            'email' => $this->sanitize($request->post('email'), 'email'),
            'subject' => $this->sanitize($request->post('subject')),
            'message' => $this->sanitize($request->post('message'))
        ];
        
        // Validate form data
        $errors = $this->validate($data, [
            'name' => 'required|max:100',
            'email' => 'required|email|max:255',
            'subject' => 'required|max:255',
            'message' => 'required|max:2000'
        ]);
        
        if (!empty($errors)) {
            $this->setFlashMessage('error', 'Please correct the errors below.');
            // In a real application, you'd pass errors back to the form
            return $this->redirect('/contact');
        }
        
        try {
            // Send email (implement email sending logic)
            $this->sendContactEmail($data);
            
            // Log the contact form submission
            $this->logActivity('contact_form_submitted', "Contact form submitted by {$data['email']}");
            
            $this->setFlashMessage('success', 'Thank you for your message. We will get back to you soon!');
            
        } catch (\Exception $e) {
            error_log("Contact form error: " . $e->getMessage());
            $this->setFlashMessage('error', 'Sorry, there was an error sending your message. Please try again later.');
        }
        
        return $this->redirect('/contact');
    }

    /**
     * Send contact email
     */
    private function sendContactEmail($data)
    {
        // This would implement actual email sending
        // For now, we'll just log it
        error_log("Contact form submission: " . json_encode($data));
        
        // TODO: Implement email sending using PHPMailer or similar
        // $mailer = new Mailer();
        // $mailer->sendContactEmail($data);
    }

    /**
     * Display privacy policy
     */
    public function privacy($request, $response, $params = [])
    {
        $this->setRequestResponse($request, $response);
        
        $content = $this->render('home/privacy', [
            'layout' => 'main',
            'title' => 'Privacy Policy - South Safari Platform',
            'meta_description' => 'Read our privacy policy to understand how we protect your personal information.',
            'page_class' => 'privacy-page'
        ]);
        
        return $response->html($content);
    }

    /**
     * Display terms of service
     */
    public function terms($request, $response, $params = [])
    {
        $this->setRequestResponse($request, $response);
        
        $content = $this->render('home/terms', [
            'layout' => 'main',
            'title' => 'Terms of Service - South Safari Platform',
            'meta_description' => 'Read our terms of service for using the South Safari platform.',
            'page_class' => 'terms-page'
        ]);
        
        return $response->html($content);
    }
}

?>
