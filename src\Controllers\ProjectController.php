<?php

namespace SouthSafari\Controllers;

/**
 * Project Controller
 * 
 * Handles project listing, viewing, and search functionality
 */
class ProjectController extends BaseController
{
    /**
     * Display project listings
     */
    public function index($request, $response, $params = [])
    {
        $this->setRequestResponse($request, $response);
        
        try {
            // Get filter parameters
            $category = $request->get('category');
            $search = $request->get('search');
            $sort = $request->get('sort', 'latest');
            $page = max(1, (int) $request->get('page', 1));
            
            // Build query
            $query = $this->buildProjectQuery($category, $search, $sort);
            
            // Get paginated results
            $perPage = defined('PROJECTS_PER_PAGE') ? PROJECTS_PER_PAGE : 12;
            $result = $this->paginate($query['sql'], $query['params'], $perPage);
            
            // Get categories for filter
            $categories = $this->getCategories();
            
            // Increment view counts for displayed projects
            $this->incrementViewCounts($result['items']);
            
            $content = $this->render('projects/index', [
                'layout' => 'main',
                'title' => 'Partnership Projects - South Safari',
                'meta_description' => 'Explore partnership opportunities with South African businesses. Find projects that match your skills and expertise.',
                'projects' => $result['items'],
                'pagination' => $result['pagination'],
                'categories' => $categories,
                'current_category' => $category,
                'current_search' => $search,
                'current_sort' => $sort,
                'page_class' => 'projects-page'
            ]);
            
            return $response->html($content);
            
        } catch (\Exception $e) {
            error_log("Projects listing error: " . $e->getMessage());
            return $response->error500();
        }
    }

    /**
     * Display single project
     */
    public function show($request, $response, $params = [])
    {
        $this->setRequestResponse($request, $response);
        
        $slug = $params['slug'] ?? '';
        
        if (empty($slug)) {
            return $response->error404();
        }
        
        try {
            // Get project details
            $project = $this->getProjectBySlug($slug);
            
            if (!$project) {
                return $response->error404('Project not found.');
            }
            
            // Check if project is published
            if ($project['status'] !== 'published') {
                return $response->error404('Project not available.');
            }
            
            // Increment view count
            $this->incrementProjectViews($project['id']);
            
            // Get related projects
            $relatedProjects = $this->getRelatedProjects($project['category'], $project['id']);
            
            // Check if user has already applied
            $hasApplied = false;
            if ($this->isLoggedIn()) {
                $hasApplied = $this->hasUserApplied($project['id'], $this->session->get('user_id'));
            }
            
            $content = $this->render('projects/show', [
                'layout' => 'main',
                'title' => $this->escape($project['title']) . ' - South Safari',
                'meta_description' => $this->truncate($this->escape($project['description']), 160),
                'project' => $project,
                'related_projects' => $relatedProjects,
                'has_applied' => $hasApplied,
                'page_class' => 'project-detail-page'
            ]);
            
            return $response->html($content);
            
        } catch (\Exception $e) {
            error_log("Project detail error: " . $e->getMessage());
            return $response->error500();
        }
    }

    /**
     * Build project query based on filters
     */
    private function buildProjectQuery($category = null, $search = null, $sort = 'latest')
    {
        $sql = "
            SELECT p.*, u.first_name, u.last_name, u.company_name,
                   COUNT(a.id) as application_count
            FROM " . $this->db->table('projects') . " p
            LEFT JOIN " . $this->db->table('users') . " u ON p.created_by = u.id
            LEFT JOIN " . $this->db->table('applications') . " a ON p.id = a.project_id
            WHERE p.status = 'published' 
            AND p.deleted_at IS NULL
        ";
        
        $params = [];
        
        // Add category filter
        if ($category) {
            $sql .= " AND p.category = ?";
            $params[] = $category;
        }
        
        // Add search filter
        if ($search) {
            $sql .= " AND (p.title LIKE ? OR p.description LIKE ? OR p.detailed_description LIKE ?)";
            $searchTerm = '%' . $search . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }
        
        $sql .= " GROUP BY p.id";
        
        // Add sorting
        switch ($sort) {
            case 'oldest':
                $sql .= " ORDER BY p.created_at ASC";
                break;
            case 'title':
                $sql .= " ORDER BY p.title ASC";
                break;
            case 'popular':
                $sql .= " ORDER BY p.views_count DESC, p.created_at DESC";
                break;
            case 'applications':
                $sql .= " ORDER BY application_count DESC, p.created_at DESC";
                break;
            case 'featured':
                $sql .= " ORDER BY p.featured DESC, p.created_at DESC";
                break;
            case 'latest':
            default:
                $sql .= " ORDER BY p.created_at DESC";
                break;
        }
        
        return ['sql' => $sql, 'params' => $params];
    }

    /**
     * Get project by slug
     */
    private function getProjectBySlug($slug)
    {
        return $this->db->selectOne("
            SELECT p.*, u.first_name, u.last_name, u.company_name, u.email as creator_email
            FROM " . $this->db->table('projects') . " p
            LEFT JOIN " . $this->db->table('users') . " u ON p.created_by = u.id
            WHERE p.slug = ? AND p.deleted_at IS NULL
        ", [$slug]);
    }

    /**
     * Get categories
     */
    private function getCategories()
    {
        return $this->db->select("
            SELECT c.*, COUNT(p.id) as project_count
            FROM " . $this->db->table('categories') . " c
            LEFT JOIN " . $this->db->table('projects') . " p ON c.slug = p.category 
                AND p.status = 'published' AND p.deleted_at IS NULL
            WHERE c.is_active = 1
            GROUP BY c.id
            ORDER BY c.sort_order ASC, c.name ASC
        ");
    }

    /**
     * Get related projects
     */
    private function getRelatedProjects($category, $excludeId, $limit = 3)
    {
        return $this->db->select("
            SELECT p.*, u.first_name, u.last_name, u.company_name
            FROM " . $this->db->table('projects') . " p
            LEFT JOIN " . $this->db->table('users') . " u ON p.created_by = u.id
            WHERE p.category = ? 
            AND p.id != ? 
            AND p.status = 'published' 
            AND p.deleted_at IS NULL
            ORDER BY p.created_at DESC
            LIMIT ?
        ", [$category, $excludeId, $limit]);
    }

    /**
     * Check if user has applied to project
     */
    private function hasUserApplied($projectId, $userId)
    {
        return $this->db->exists('applications', 'project_id = ? AND user_id = ?', [$projectId, $userId]);
    }

    /**
     * Increment project view count
     */
    private function incrementProjectViews($projectId)
    {
        $this->db->query(
            "UPDATE " . $this->db->table('projects') . " SET views_count = views_count + 1 WHERE id = ?",
            [$projectId]
        );
    }

    /**
     * Increment view counts for multiple projects
     */
    private function incrementViewCounts($projects)
    {
        if (empty($projects)) return;
        
        $projectIds = array_column($projects, 'id');
        $placeholders = str_repeat('?,', count($projectIds) - 1) . '?';
        
        $this->db->query(
            "UPDATE " . $this->db->table('projects') . " SET views_count = views_count + 1 WHERE id IN ($placeholders)",
            $projectIds
        );
    }

    /**
     * Search projects (AJAX endpoint)
     */
    public function search($request, $response, $params = [])
    {
        $this->setRequestResponse($request, $response);
        
        if (!$this->isAjaxRequest()) {
            return $response->error404();
        }
        
        $query = $request->get('q', '');
        
        if (strlen($query) < 2) {
            return $this->json(['status' => 'success', 'results' => []]);
        }
        
        try {
            $results = $this->db->select("
                SELECT p.id, p.title, p.slug, p.category, p.description
                FROM " . $this->db->table('projects') . " p
                WHERE p.status = 'published' 
                AND p.deleted_at IS NULL
                AND (p.title LIKE ? OR p.description LIKE ?)
                ORDER BY p.title ASC
                LIMIT 10
            ", ["%{$query}%", "%{$query}%"]);
            
            return $this->json([
                'status' => 'success',
                'results' => $results
            ]);
            
        } catch (\Exception $e) {
            error_log("Project search error: " . $e->getMessage());
            return $this->error('Search failed');
        }
    }
}

?>
