<?php

namespace SouthSafari\Core;

/**
 * Configuration Class
 * 
 * Handles application configuration management
 */
class Config
{
    private static $instance = null;
    private $config = [];

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->loadConfiguration();
    }

    /**
     * Get singleton instance
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Load configuration from constants and files
     */
    private function loadConfiguration()
    {
        // Load from defined constants
        $this->loadFromConstants();
        
        // Load from configuration files
        $this->loadFromFiles();
    }

    /**
     * Load configuration from defined constants
     */
    private function loadFromConstants()
    {
        $constants = get_defined_constants(true)['user'] ?? [];
        
        foreach ($constants as $name => $value) {
            // Convert constant names to config keys
            $key = strtolower($name);
            $this->config[$key] = $value;
        }
    }

    /**
     * Load configuration from files
     */
    private function loadFromFiles()
    {
        $configPath = ROOT_PATH . '/config/';
        
        // Load additional config files if they exist
        $configFiles = [
            'app.php',
            'database.php',
            'email.php',
            'cache.php'
        ];
        
        foreach ($configFiles as $file) {
            $filePath = $configPath . $file;
            if (file_exists($filePath)) {
                $fileConfig = include $filePath;
                if (is_array($fileConfig)) {
                    $this->config = array_merge($this->config, $fileConfig);
                }
            }
        }
    }

    /**
     * Get configuration value
     */
    public function get($key, $default = null)
    {
        // Support dot notation for nested arrays
        if (strpos($key, '.') !== false) {
            return $this->getNestedValue($key, $default);
        }
        
        return $this->config[$key] ?? $default;
    }

    /**
     * Set configuration value
     */
    public function set($key, $value)
    {
        // Support dot notation for nested arrays
        if (strpos($key, '.') !== false) {
            $this->setNestedValue($key, $value);
        } else {
            $this->config[$key] = $value;
        }
    }

    /**
     * Check if configuration key exists
     */
    public function has($key)
    {
        if (strpos($key, '.') !== false) {
            return $this->getNestedValue($key) !== null;
        }
        
        return isset($this->config[$key]);
    }

    /**
     * Get all configuration
     */
    public function all()
    {
        return $this->config;
    }

    /**
     * Get nested configuration value using dot notation
     */
    private function getNestedValue($key, $default = null)
    {
        $keys = explode('.', $key);
        $value = $this->config;
        
        foreach ($keys as $k) {
            if (!is_array($value) || !isset($value[$k])) {
                return $default;
            }
            $value = $value[$k];
        }
        
        return $value;
    }

    /**
     * Set nested configuration value using dot notation
     */
    private function setNestedValue($key, $value)
    {
        $keys = explode('.', $key);
        $config = &$this->config;
        
        foreach ($keys as $k) {
            if (!isset($config[$k]) || !is_array($config[$k])) {
                $config[$k] = [];
            }
            $config = &$config[$k];
        }
        
        $config = $value;
    }

    /**
     * Load environment-specific configuration
     */
    public function loadEnvironment($environment)
    {
        $envFile = ROOT_PATH . "/config/environments/{$environment}.php";
        
        if (file_exists($envFile)) {
            $envConfig = include $envFile;
            if (is_array($envConfig)) {
                $this->config = array_merge($this->config, $envConfig);
            }
        }
    }

    /**
     * Get database configuration
     */
    public function getDatabase()
    {
        return [
            'host' => $this->get('db_host', 'localhost'),
            'name' => $this->get('db_name', 'south_safari'),
            'user' => $this->get('db_user', 'root'),
            'pass' => $this->get('db_pass', ''),
            'charset' => $this->get('db_charset', 'utf8mb4'),
            'prefix' => $this->get('db_prefix', 'ss_')
        ];
    }

    /**
     * Get email configuration
     */
    public function getEmail()
    {
        return [
            'driver' => $this->get('mail_driver', 'smtp'),
            'host' => $this->get('mail_host', 'localhost'),
            'port' => $this->get('mail_port', 587),
            'username' => $this->get('mail_username', ''),
            'password' => $this->get('mail_password', ''),
            'encryption' => $this->get('mail_encryption', 'tls'),
            'from_address' => $this->get('mail_from_address', '<EMAIL>'),
            'from_name' => $this->get('mail_from_name', 'South Safari')
        ];
    }

    /**
     * Get application configuration
     */
    public function getApp()
    {
        return [
            'name' => $this->get('app_name', 'South Safari'),
            'version' => $this->get('app_version', '1.0.0'),
            'url' => $this->get('app_url', 'http://localhost'),
            'timezone' => $this->get('app_timezone', 'UTC'),
            'locale' => $this->get('app_locale', 'en_US'),
            'debug' => $this->get('debug_mode', false)
        ];
    }

    /**
     * Get security configuration
     */
    public function getSecurity()
    {
        return [
            'secret_key' => $this->get('secret_key', ''),
            'csrf_token_name' => $this->get('csrf_token_name', 'csrf_token'),
            'session_name' => $this->get('session_name', 'south_safari_session'),
            'session_lifetime' => $this->get('session_lifetime', 7200),
            'password_min_length' => $this->get('password_min_length', 8)
        ];
    }

    /**
     * Get file upload configuration
     */
    public function getUpload()
    {
        return [
            'max_size' => $this->get('upload_max_size', 50 * 1024 * 1024),
            'allowed_types' => explode(',', $this->get('upload_allowed_types', 'jpg,jpeg,png,gif,pdf,doc,docx')),
            'path' => $this->get('upload_path', 'public/uploads/'),
            'avatar_max_size' => $this->get('avatar_max_size', 5 * 1024 * 1024)
        ];
    }

    /**
     * Get pagination configuration
     */
    public function getPagination()
    {
        return [
            'items_per_page' => $this->get('items_per_page', 20),
            'projects_per_page' => $this->get('projects_per_page', 12),
            'messages_per_page' => $this->get('messages_per_page', 15),
            'applications_per_page' => $this->get('applications_per_page', 25)
        ];
    }

    /**
     * Check if feature is enabled
     */
    public function isFeatureEnabled($feature)
    {
        return (bool) $this->get("feature_{$feature}", false);
    }

    /**
     * Get feature flags
     */
    public function getFeatures()
    {
        $features = [];
        
        foreach ($this->config as $key => $value) {
            if (strpos($key, 'feature_') === 0) {
                $featureName = substr($key, 8); // Remove 'feature_' prefix
                $features[$featureName] = (bool) $value;
            }
        }
        
        return $features;
    }

    /**
     * Save configuration to file
     */
    public function save($filename = 'runtime.php')
    {
        $configPath = ROOT_PATH . '/config/' . $filename;
        $configData = "<?php\n\nreturn " . var_export($this->config, true) . ";\n";
        
        return file_put_contents($configPath, $configData) !== false;
    }

    /**
     * Merge configuration array
     */
    public function merge(array $config)
    {
        $this->config = array_merge($this->config, $config);
    }

    /**
     * Clear configuration
     */
    public function clear()
    {
        $this->config = [];
    }

    /**
     * Get configuration as JSON
     */
    public function toJson()
    {
        return json_encode($this->config, JSON_PRETTY_PRINT);
    }

    /**
     * Load configuration from JSON
     */
    public function fromJson($json)
    {
        $config = json_decode($json, true);
        if (is_array($config)) {
            $this->config = array_merge($this->config, $config);
            return true;
        }
        return false;
    }
}

?>
