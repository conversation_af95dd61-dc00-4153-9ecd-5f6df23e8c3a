<?php

namespace SouthSafari\Core;

use PDO;
use PDOException;

/**
 * Database Connection and Query Builder
 * 
 * Handles database connections and provides a simple query builder interface
 */
class Database
{
    private static $instance = null;
    private $connection = null;
    private $host;
    private $database;
    private $username;
    private $password;
    private $charset;
    private $prefix;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->host = DB_HOST;
        $this->database = DB_NAME;
        $this->username = DB_USER;
        $this->password = DB_PASS;
        $this->charset = DB_CHARSET;
        $this->prefix = DB_PREFIX;

        $this->connect();
    }

    /**
     * Get singleton instance
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Create database connection
     */
    private function connect()
    {
        try {
            $dsn = "mysql:host={$this->host};dbname={$this->database};charset={$this->charset}";
            
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->charset}"
            ];

            $this->connection = new PDO($dsn, $this->username, $this->password, $options);
            
        } catch (PDOException $e) {
            $this->handleConnectionError($e);
        }
    }

    /**
     * Handle connection errors
     */
    private function handleConnectionError($exception)
    {
        $error = "Database connection failed: " . $exception->getMessage();
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            die($error);
        } else {
            error_log($error);
            die("Database connection failed. Please try again later.");
        }
    }

    /**
     * Get PDO connection
     */
    public function getConnection()
    {
        return $this->connection;
    }

    /**
     * Add table prefix to table name
     */
    public function table($table)
    {
        return $this->prefix . $table;
    }

    /**
     * Execute a prepared statement
     */
    public function query($sql, $params = [])
    {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            $this->handleQueryError($e, $sql, $params);
        }
    }

    /**
     * Select records from database
     */
    public function select($sql, $params = [])
    {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }

    /**
     * Select single record from database
     */
    public function selectOne($sql, $params = [])
    {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }

    /**
     * Insert record into database
     */
    public function insert($table, $data)
    {
        $table = $this->table($table);
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        
        $this->query($sql, $data);
        return $this->connection->lastInsertId();
    }

    /**
     * Update records in database
     */
    public function update($table, $data, $where, $whereParams = [])
    {
        $table = $this->table($table);
        $setClause = [];
        
        foreach ($data as $key => $value) {
            $setClause[] = "{$key} = :{$key}";
        }
        
        $setClause = implode(', ', $setClause);
        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        
        $params = array_merge($data, $whereParams);
        $stmt = $this->query($sql, $params);
        
        return $stmt->rowCount();
    }

    /**
     * Delete records from database
     */
    public function delete($table, $where, $params = [])
    {
        $table = $this->table($table);
        $sql = "DELETE FROM {$table} WHERE {$where}";
        
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }

    /**
     * Count records in table
     */
    public function count($table, $where = '1=1', $params = [])
    {
        $table = $this->table($table);
        $sql = "SELECT COUNT(*) as count FROM {$table} WHERE {$where}";
        
        $result = $this->selectOne($sql, $params);
        return (int) $result['count'];
    }

    /**
     * Check if record exists
     */
    public function exists($table, $where, $params = [])
    {
        return $this->count($table, $where, $params) > 0;
    }

    /**
     * Begin transaction
     */
    public function beginTransaction()
    {
        return $this->connection->beginTransaction();
    }

    /**
     * Commit transaction
     */
    public function commit()
    {
        return $this->connection->commit();
    }

    /**
     * Rollback transaction
     */
    public function rollback()
    {
        return $this->connection->rollback();
    }

    /**
     * Execute multiple queries in a transaction
     */
    public function transaction($callback)
    {
        $this->beginTransaction();
        
        try {
            $result = $callback($this);
            $this->commit();
            return $result;
        } catch (\Exception $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * Get table schema information
     */
    public function getTableSchema($table)
    {
        $table = $this->table($table);
        $sql = "DESCRIBE {$table}";
        return $this->select($sql);
    }

    /**
     * Check if table exists
     */
    public function tableExists($table)
    {
        $table = $this->table($table);
        $sql = "SHOW TABLES LIKE ?";
        $result = $this->selectOne($sql, [$table]);
        return !empty($result);
    }

    /**
     * Get last insert ID
     */
    public function lastInsertId()
    {
        return $this->connection->lastInsertId();
    }

    /**
     * Escape string for SQL
     */
    public function escape($string)
    {
        return $this->connection->quote($string);
    }

    /**
     * Handle query errors
     */
    private function handleQueryError($exception, $sql, $params)
    {
        $error = "Database query failed: " . $exception->getMessage();
        $error .= "\nSQL: " . $sql;
        $error .= "\nParams: " . json_encode($params);
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            die("<pre>$error</pre>");
        } else {
            error_log($error);
            throw new \Exception("Database query failed");
        }
    }

    /**
     * Simple query builder for SELECT statements
     */
    public function table_builder($table)
    {
        return new QueryBuilder($this, $table);
    }

    /**
     * Close database connection
     */
    public function close()
    {
        $this->connection = null;
    }

    /**
     * Destructor
     */
    public function __destruct()
    {
        $this->close();
    }
}

/**
 * Simple Query Builder Class
 */
class QueryBuilder
{
    private $db;
    private $table;
    private $select = '*';
    private $where = [];
    private $orderBy = [];
    private $limit = null;
    private $offset = null;
    private $joins = [];

    public function __construct($db, $table)
    {
        $this->db = $db;
        $this->table = $db->table($table);
    }

    public function select($columns = '*')
    {
        $this->select = is_array($columns) ? implode(', ', $columns) : $columns;
        return $this;
    }

    public function where($column, $operator, $value = null)
    {
        if ($value === null) {
            $value = $operator;
            $operator = '=';
        }
        
        $this->where[] = [
            'column' => $column,
            'operator' => $operator,
            'value' => $value,
            'type' => 'AND'
        ];
        
        return $this;
    }

    public function orWhere($column, $operator, $value = null)
    {
        if ($value === null) {
            $value = $operator;
            $operator = '=';
        }
        
        $this->where[] = [
            'column' => $column,
            'operator' => $operator,
            'value' => $value,
            'type' => 'OR'
        ];
        
        return $this;
    }

    public function orderBy($column, $direction = 'ASC')
    {
        $this->orderBy[] = "{$column} {$direction}";
        return $this;
    }

    public function limit($limit, $offset = null)
    {
        $this->limit = $limit;
        if ($offset !== null) {
            $this->offset = $offset;
        }
        return $this;
    }

    public function get()
    {
        $sql = $this->buildSelectQuery();
        $params = $this->getWhereParams();
        return $this->db->select($sql, $params);
    }

    public function first()
    {
        $this->limit(1);
        $results = $this->get();
        return !empty($results) ? $results[0] : null;
    }

    private function buildSelectQuery()
    {
        $sql = "SELECT {$this->select} FROM {$this->table}";
        
        if (!empty($this->where)) {
            $sql .= " WHERE " . $this->buildWhereClause();
        }
        
        if (!empty($this->orderBy)) {
            $sql .= " ORDER BY " . implode(', ', $this->orderBy);
        }
        
        if ($this->limit !== null) {
            $sql .= " LIMIT {$this->limit}";
            if ($this->offset !== null) {
                $sql .= " OFFSET {$this->offset}";
            }
        }
        
        return $sql;
    }

    private function buildWhereClause()
    {
        $clauses = [];
        foreach ($this->where as $index => $condition) {
            $placeholder = ":where_{$index}";
            $clause = "{$condition['column']} {$condition['operator']} {$placeholder}";
            
            if ($index > 0) {
                $clause = "{$condition['type']} {$clause}";
            }
            
            $clauses[] = $clause;
        }
        
        return implode(' ', $clauses);
    }

    private function getWhereParams()
    {
        $params = [];
        foreach ($this->where as $index => $condition) {
            $params["where_{$index}"] = $condition['value'];
        }
        return $params;
    }
}

?>
