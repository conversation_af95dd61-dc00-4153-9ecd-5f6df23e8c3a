<?php

namespace SouthSafari\Core;

/**
 * Request Class
 * 
 * Handles HTTP request data and provides convenient methods to access it
 */
class Request
{
    private $get;
    private $post;
    private $files;
    private $server;
    private $headers;
    private $method;
    private $uri;
    private $body;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->get = $_GET;
        $this->post = $_POST;
        $this->files = $_FILES;
        $this->server = $_SERVER;
        $this->headers = $this->parseHeaders();
        $this->method = $this->parseMethod();
        $this->uri = $this->parseUri();
        $this->body = $this->parseBody();
    }

    /**
     * Parse HTTP headers
     */
    private function parseHeaders()
    {
        $headers = [];
        
        foreach ($this->server as $key => $value) {
            if (strpos($key, 'HTTP_') === 0) {
                $header = str_replace('HTTP_', '', $key);
                $header = str_replace('_', '-', $header);
                $header = ucwords(strtolower($header), '-');
                $headers[$header] = $value;
            }
        }
        
        return $headers;
    }

    /**
     * Parse HTTP method
     */
    private function parseMethod()
    {
        $method = strtoupper($this->server['REQUEST_METHOD'] ?? 'GET');
        
        // Check for method override
        if ($method === 'POST') {
            if (isset($this->post['_method'])) {
                $method = strtoupper($this->post['_method']);
            } elseif (isset($this->headers['X-Http-Method-Override'])) {
                $method = strtoupper($this->headers['X-Http-Method-Override']);
            }
        }
        
        return $method;
    }

    /**
     * Parse request URI
     */
    private function parseUri()
    {
        $uri = $this->server['REQUEST_URI'] ?? '/';
        
        // Remove query string
        if (($pos = strpos($uri, '?')) !== false) {
            $uri = substr($uri, 0, $pos);
        }
        
        // Remove base path if application is in subdirectory
        $scriptName = dirname($this->server['SCRIPT_NAME']);
        if ($scriptName !== '/' && strpos($uri, $scriptName) === 0) {
            $uri = substr($uri, strlen($scriptName));
        }
        
        return '/' . ltrim($uri, '/');
    }

    /**
     * Parse request body
     */
    private function parseBody()
    {
        $body = file_get_contents('php://input');
        
        // Try to decode JSON
        if ($this->isJson()) {
            $decoded = json_decode($body, true);
            return $decoded !== null ? $decoded : $body;
        }
        
        return $body;
    }

    /**
     * Get HTTP method
     */
    public function getMethod()
    {
        return $this->method;
    }

    /**
     * Get request URI
     */
    public function getUri()
    {
        return $this->uri;
    }

    /**
     * Get all GET parameters
     */
    public function query()
    {
        return $this->get;
    }

    /**
     * Get specific GET parameter
     */
    public function get($key, $default = null)
    {
        return $this->get[$key] ?? $default;
    }

    /**
     * Get all POST parameters
     */
    public function all()
    {
        return array_merge($this->post, $this->body);
    }

    /**
     * Get specific POST parameter
     */
    public function post($key, $default = null)
    {
        if (is_array($this->body) && isset($this->body[$key])) {
            return $this->body[$key];
        }
        
        return $this->post[$key] ?? $default;
    }

    /**
     * Get input value (POST or GET)
     */
    public function input($key, $default = null)
    {
        return $this->post($key) ?? $this->get($key, $default);
    }

    /**
     * Check if input exists
     */
    public function has($key)
    {
        return isset($this->post[$key]) || isset($this->get[$key]) || 
               (is_array($this->body) && isset($this->body[$key]));
    }

    /**
     * Get only specified inputs
     */
    public function only($keys)
    {
        $keys = is_array($keys) ? $keys : func_get_args();
        $result = [];
        
        foreach ($keys as $key) {
            if ($this->has($key)) {
                $result[$key] = $this->input($key);
            }
        }
        
        return $result;
    }

    /**
     * Get all inputs except specified
     */
    public function except($keys)
    {
        $keys = is_array($keys) ? $keys : func_get_args();
        $all = $this->all();
        
        foreach ($keys as $key) {
            unset($all[$key]);
        }
        
        return $all;
    }

    /**
     * Get uploaded file
     */
    public function file($key)
    {
        return isset($this->files[$key]) ? new UploadedFile($this->files[$key]) : null;
    }

    /**
     * Check if file was uploaded
     */
    public function hasFile($key)
    {
        return isset($this->files[$key]) && $this->files[$key]['error'] === UPLOAD_ERR_OK;
    }

    /**
     * Get all uploaded files
     */
    public function files()
    {
        $files = [];
        foreach ($this->files as $key => $file) {
            $files[$key] = new UploadedFile($file);
        }
        return $files;
    }

    /**
     * Get HTTP header
     */
    public function header($key, $default = null)
    {
        $key = ucwords(strtolower($key), '-');
        return $this->headers[$key] ?? $default;
    }

    /**
     * Get all headers
     */
    public function headers()
    {
        return $this->headers;
    }

    /**
     * Get server variable
     */
    public function server($key, $default = null)
    {
        return $this->server[$key] ?? $default;
    }

    /**
     * Get client IP address
     */
    public function ip()
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($this->server[$key])) {
                $ip = $this->server[$key];
                // Handle comma-separated IPs
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                return $ip;
            }
        }
        
        return '127.0.0.1';
    }

    /**
     * Get user agent
     */
    public function userAgent()
    {
        return $this->server['HTTP_USER_AGENT'] ?? '';
    }

    /**
     * Check if request is AJAX
     */
    public function isAjax()
    {
        return strtolower($this->header('X-Requested-With', '')) === 'xmlhttprequest';
    }

    /**
     * Check if request is JSON
     */
    public function isJson()
    {
        $contentType = $this->header('Content-Type', '');
        return strpos($contentType, 'application/json') !== false;
    }

    /**
     * Check if request is secure (HTTPS)
     */
    public function isSecure()
    {
        return (!empty($this->server['HTTPS']) && $this->server['HTTPS'] !== 'off') ||
               $this->server['SERVER_PORT'] == 443 ||
               strtolower($this->header('X-Forwarded-Proto', '')) === 'https';
    }

    /**
     * Get request scheme
     */
    public function getScheme()
    {
        return $this->isSecure() ? 'https' : 'http';
    }

    /**
     * Get host name
     */
    public function getHost()
    {
        return $this->server['HTTP_HOST'] ?? $this->server['SERVER_NAME'] ?? 'localhost';
    }

    /**
     * Get full URL
     */
    public function getUrl()
    {
        return $this->getScheme() . '://' . $this->getHost() . $this->uri;
    }

    /**
     * Validate input data
     */
    public function validate($rules)
    {
        $validator = new Validator($this->all(), $rules);
        return $validator->validate();
    }

    /**
     * Sanitize input
     */
    public function sanitize($key, $filter = FILTER_SANITIZE_STRING)
    {
        $value = $this->input($key);
        return $value !== null ? filter_var($value, $filter) : null;
    }

    /**
     * Get raw request body
     */
    public function getBody()
    {
        return $this->body;
    }
}

/**
 * Uploaded File Class
 */
class UploadedFile
{
    private $file;

    public function __construct($file)
    {
        $this->file = $file;
    }

    public function getName()
    {
        return $this->file['name'];
    }

    public function getSize()
    {
        return $this->file['size'];
    }

    public function getType()
    {
        return $this->file['type'];
    }

    public function getTmpName()
    {
        return $this->file['tmp_name'];
    }

    public function getError()
    {
        return $this->file['error'];
    }

    public function isValid()
    {
        return $this->file['error'] === UPLOAD_ERR_OK;
    }

    public function getExtension()
    {
        return pathinfo($this->file['name'], PATHINFO_EXTENSION);
    }

    public function move($destination)
    {
        if (!$this->isValid()) {
            return false;
        }

        $directory = dirname($destination);
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }

        return move_uploaded_file($this->file['tmp_name'], $destination);
    }
}

?>
