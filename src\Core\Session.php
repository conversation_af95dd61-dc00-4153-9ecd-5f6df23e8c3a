<?php

namespace SouthSafari\Core;

/**
 * Session Class
 * 
 * Handles session management with security features
 */
class Session
{
    private $started = false;
    private $sessionName;
    private $sessionLifetime;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->sessionName = defined('SESSION_NAME') ? SESSION_NAME : 'south_safari_session';
        $this->sessionLifetime = defined('SESSION_LIFETIME') ? SESSION_LIFETIME : 7200; // 2 hours
        
        $this->start();
    }

    /**
     * Start the session
     */
    public function start()
    {
        if ($this->started || session_status() === PHP_SESSION_ACTIVE) {
            return;
        }

        // Configure session settings
        $this->configureSession();
        
        // Set session name
        session_name($this->sessionName);
        
        // Start session
        if (session_start()) {
            $this->started = true;
            
            // Regenerate session ID periodically for security
            $this->regenerateIdIfNeeded();
            
            // Check session validity
            $this->validateSession();
        }
    }

    /**
     * Configure session settings
     */
    private function configureSession()
    {
        // Set session cookie parameters
        $cookieParams = [
            'lifetime' => $this->sessionLifetime,
            'path' => '/',
            'domain' => '',
            'secure' => $this->isSecureConnection(),
            'httponly' => true,
            'samesite' => 'Strict'
        ];
        
        session_set_cookie_params($cookieParams);
        
        // Set session configuration
        ini_set('session.gc_maxlifetime', $this->sessionLifetime);
        ini_set('session.cookie_lifetime', $this->sessionLifetime);
        ini_set('session.use_strict_mode', 1);
        ini_set('session.use_cookies', 1);
        ini_set('session.use_only_cookies', 1);
        ini_set('session.cookie_httponly', 1);
        ini_set('session.cookie_secure', $this->isSecureConnection() ? 1 : 0);
    }

    /**
     * Check if connection is secure (HTTPS)
     */
    private function isSecureConnection()
    {
        return (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ||
               $_SERVER['SERVER_PORT'] == 443 ||
               (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https');
    }

    /**
     * Regenerate session ID if needed
     */
    private function regenerateIdIfNeeded()
    {
        $regenerateInterval = 1800; // 30 minutes
        
        if (!isset($_SESSION['last_regeneration'])) {
            $_SESSION['last_regeneration'] = time();
        } elseif (time() - $_SESSION['last_regeneration'] > $regenerateInterval) {
            $this->regenerateId();
        }
    }

    /**
     * Validate session
     */
    private function validateSession()
    {
        // Check session timeout
        if (isset($_SESSION['last_activity'])) {
            if (time() - $_SESSION['last_activity'] > $this->sessionLifetime) {
                $this->destroy();
                return;
            }
        }
        
        // Update last activity
        $_SESSION['last_activity'] = time();
        
        // Validate user agent (basic fingerprinting)
        $currentUserAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        if (isset($_SESSION['user_agent'])) {
            if ($_SESSION['user_agent'] !== $currentUserAgent) {
                $this->destroy();
                return;
            }
        } else {
            $_SESSION['user_agent'] = $currentUserAgent;
        }
        
        // Validate IP address (optional, can be problematic with proxies)
        if (defined('SESSION_VALIDATE_IP') && SESSION_VALIDATE_IP) {
            $currentIp = $this->getClientIp();
            if (isset($_SESSION['ip_address'])) {
                if ($_SESSION['ip_address'] !== $currentIp) {
                    $this->destroy();
                    return;
                }
            } else {
                $_SESSION['ip_address'] = $currentIp;
            }
        }
    }

    /**
     * Get client IP address
     */
    private function getClientIp()
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                // Handle comma-separated IPs
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                return $ip;
            }
        }
        
        return '127.0.0.1';
    }

    /**
     * Regenerate session ID
     */
    public function regenerateId($deleteOldSession = true)
    {
        if ($this->started) {
            session_regenerate_id($deleteOldSession);
            $_SESSION['last_regeneration'] = time();
        }
    }

    /**
     * Set session value
     */
    public function set($key, $value)
    {
        if (!$this->started) {
            $this->start();
        }
        
        $_SESSION[$key] = $value;
    }

    /**
     * Get session value
     */
    public function get($key, $default = null)
    {
        if (!$this->started) {
            $this->start();
        }
        
        return $_SESSION[$key] ?? $default;
    }

    /**
     * Check if session key exists
     */
    public function has($key)
    {
        if (!$this->started) {
            $this->start();
        }
        
        return isset($_SESSION[$key]);
    }

    /**
     * Remove session value
     */
    public function remove($key)
    {
        if (!$this->started) {
            $this->start();
        }
        
        unset($_SESSION[$key]);
    }

    /**
     * Get all session data
     */
    public function all()
    {
        if (!$this->started) {
            $this->start();
        }
        
        return $_SESSION;
    }

    /**
     * Clear all session data
     */
    public function clear()
    {
        if (!$this->started) {
            $this->start();
        }
        
        $_SESSION = [];
    }

    /**
     * Destroy session
     */
    public function destroy()
    {
        if (!$this->started) {
            return;
        }
        
        // Clear session data
        $_SESSION = [];
        
        // Delete session cookie
        if (ini_get('session.use_cookies')) {
            $params = session_get_cookie_params();
            setcookie(
                session_name(),
                '',
                time() - 42000,
                $params['path'],
                $params['domain'],
                $params['secure'],
                $params['httponly']
            );
        }
        
        // Destroy session
        session_destroy();
        $this->started = false;
    }

    /**
     * Flash data (available for next request only)
     */
    public function flash($key, $value = null)
    {
        if ($value === null) {
            // Get flash data
            $flashData = $this->get('_flash_data', []);
            $data = $flashData[$key] ?? null;
            
            // Remove from flash data
            unset($flashData[$key]);
            $this->set('_flash_data', $flashData);
            
            return $data;
        } else {
            // Set flash data
            $flashData = $this->get('_flash_data', []);
            $flashData[$key] = $value;
            $this->set('_flash_data', $flashData);
        }
    }

    /**
     * Keep flash data for another request
     */
    public function reflash($keys = null)
    {
        $flashData = $this->get('_flash_data', []);
        $newFlashData = $this->get('_flash_new', []);
        
        if ($keys === null) {
            $newFlashData = array_merge($newFlashData, $flashData);
        } else {
            $keys = is_array($keys) ? $keys : [$keys];
            foreach ($keys as $key) {
                if (isset($flashData[$key])) {
                    $newFlashData[$key] = $flashData[$key];
                }
            }
        }
        
        $this->set('_flash_new', $newFlashData);
    }

    /**
     * Get session ID
     */
    public function getId()
    {
        return session_id();
    }

    /**
     * Set session ID
     */
    public function setId($id)
    {
        session_id($id);
    }

    /**
     * Get session name
     */
    public function getName()
    {
        return session_name();
    }

    /**
     * Check if session is started
     */
    public function isStarted()
    {
        return $this->started;
    }

    /**
     * Save session data
     */
    public function save()
    {
        if ($this->started) {
            session_write_close();
        }
    }

    /**
     * Get CSRF token
     */
    public function getCsrfToken()
    {
        if (!$this->has('csrf_token')) {
            $this->set('csrf_token', bin2hex(random_bytes(32)));
        }
        
        return $this->get('csrf_token');
    }

    /**
     * Validate CSRF token
     */
    public function validateCsrfToken($token)
    {
        $sessionToken = $this->get('csrf_token');
        return $token && $sessionToken && hash_equals($sessionToken, $token);
    }
}

?>
