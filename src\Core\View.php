<?php

namespace SouthSafari\Core;

/**
 * View Class
 * 
 * Handles template rendering and view management
 */
class View
{
    private $viewPath;
    private $layoutPath;
    private $data = [];
    private $sections = [];
    private $currentSection = null;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->viewPath = ROOT_PATH . '/src/Views/';
        $this->layoutPath = ROOT_PATH . '/src/Views/layouts/';
    }

    /**
     * Render a view template
     */
    public function render($template, $data = [])
    {
        $this->data = array_merge($this->data, $data);
        
        $templateFile = $this->viewPath . $template . '.php';
        
        if (!file_exists($templateFile)) {
            throw new \Exception("View template not found: {$template}");
        }

        // Start output buffering
        ob_start();
        
        // Extract data to variables
        extract($this->data);
        
        // Include the template
        include $templateFile;
        
        // Get the content
        $content = ob_get_clean();
        
        // If a layout is specified, render it
        if (isset($this->data['layout'])) {
            return $this->renderLayout($this->data['layout'], $content);
        }
        
        return $content;
    }

    /**
     * Render layout with content
     */
    private function renderLayout($layout, $content)
    {
        $layoutFile = $this->layoutPath . $layout . '.php';
        
        if (!file_exists($layoutFile)) {
            throw new \Exception("Layout not found: {$layout}");
        }

        // Add content to data
        $this->data['content'] = $content;
        
        // Start output buffering
        ob_start();
        
        // Extract data to variables
        extract($this->data);
        
        // Include the layout
        include $layoutFile;
        
        // Get the final content
        return ob_get_clean();
    }

    /**
     * Set global view data
     */
    public function share($key, $value = null)
    {
        if (is_array($key)) {
            $this->data = array_merge($this->data, $key);
        } else {
            $this->data[$key] = $value;
        }
    }

    /**
     * Start a section
     */
    public function section($name)
    {
        $this->currentSection = $name;
        ob_start();
    }

    /**
     * End a section
     */
    public function endSection()
    {
        if ($this->currentSection) {
            $this->sections[$this->currentSection] = ob_get_clean();
            $this->currentSection = null;
        }
    }

    /**
     * Yield a section
     */
    public function yieldSection($name, $default = '')
    {
        return $this->sections[$name] ?? $default;
    }

    /**
     * Include a partial view
     */
    public function partial($template, $data = [])
    {
        $partialFile = $this->viewPath . 'partials/' . $template . '.php';
        
        if (!file_exists($partialFile)) {
            throw new \Exception("Partial view not found: {$template}");
        }

        // Merge data
        $partialData = array_merge($this->data, $data);
        
        // Start output buffering
        ob_start();
        
        // Extract data to variables
        extract($partialData);
        
        // Include the partial
        include $partialFile;
        
        // Return the content
        return ob_get_clean();
    }

    /**
     * Escape HTML
     */
    public function escape($string)
    {
        return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
    }

    /**
     * Generate URL
     */
    public function url($path = '')
    {
        $baseUrl = defined('APP_URL') ? APP_URL : '';
        return rtrim($baseUrl, '/') . '/' . ltrim($path, '/');
    }

    /**
     * Generate asset URL
     */
    public function asset($path)
    {
        $baseUrl = defined('ASSETS_URL') ? ASSETS_URL : $this->url('assets');
        return rtrim($baseUrl, '/') . '/' . ltrim($path, '/');
    }

    /**
     * Format date
     */
    public function formatDate($date, $format = null)
    {
        if (!$date) return '';
        
        $format = $format ?? (defined('DATE_FORMAT') ? DATE_FORMAT : 'Y-m-d');
        
        if (is_string($date)) {
            $date = new \DateTime($date);
        }
        
        return $date->format($format);
    }

    /**
     * Format datetime
     */
    public function formatDateTime($datetime, $format = null)
    {
        if (!$datetime) return '';
        
        $format = $format ?? (defined('DATETIME_FORMAT') ? DATETIME_FORMAT : 'Y-m-d H:i:s');
        
        if (is_string($datetime)) {
            $datetime = new \DateTime($datetime);
        }
        
        return $datetime->format($format);
    }

    /**
     * Truncate text
     */
    public function truncate($text, $length = 100, $suffix = '...')
    {
        if (strlen($text) <= $length) {
            return $text;
        }
        
        return substr($text, 0, $length) . $suffix;
    }

    /**
     * Generate pagination links
     */
    public function pagination($pagination, $baseUrl = '')
    {
        if ($pagination['total_pages'] <= 1) {
            return '';
        }

        $html = '<nav aria-label="Page navigation"><ul class="pagination">';
        
        // Previous button
        if ($pagination['has_prev']) {
            $prevPage = $pagination['current_page'] - 1;
            $html .= '<li class="page-item"><a class="page-link" href="' . $baseUrl . '?page=' . $prevPage . '">Previous</a></li>';
        } else {
            $html .= '<li class="page-item disabled"><span class="page-link">Previous</span></li>';
        }
        
        // Page numbers
        $start = max(1, $pagination['current_page'] - 2);
        $end = min($pagination['total_pages'], $pagination['current_page'] + 2);
        
        for ($i = $start; $i <= $end; $i++) {
            if ($i == $pagination['current_page']) {
                $html .= '<li class="page-item active"><span class="page-link">' . $i . '</span></li>';
            } else {
                $html .= '<li class="page-item"><a class="page-link" href="' . $baseUrl . '?page=' . $i . '">' . $i . '</a></li>';
            }
        }
        
        // Next button
        if ($pagination['has_next']) {
            $nextPage = $pagination['current_page'] + 1;
            $html .= '<li class="page-item"><a class="page-link" href="' . $baseUrl . '?page=' . $nextPage . '">Next</a></li>';
        } else {
            $html .= '<li class="page-item disabled"><span class="page-link">Next</span></li>';
        }
        
        $html .= '</ul></nav>';
        
        return $html;
    }

    /**
     * Render flash messages
     */
    public function flashMessages($messages = [])
    {
        if (empty($messages)) {
            return '';
        }

        $html = '';
        foreach ($messages as $message) {
            $type = $message['type'];
            $text = $this->escape($message['message']);
            
            // Map message types to Bootstrap alert classes
            $alertClass = [
                'success' => 'alert-success',
                'error' => 'alert-danger',
                'warning' => 'alert-warning',
                'info' => 'alert-info'
            ][$type] ?? 'alert-info';
            
            $html .= '<div class="alert ' . $alertClass . ' alert-dismissible fade show" role="alert">';
            $html .= $text;
            $html .= '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
            $html .= '</div>';
        }
        
        return $html;
    }

    /**
     * Check if view file exists
     */
    public function exists($template)
    {
        return file_exists($this->viewPath . $template . '.php');
    }

    /**
     * Set view path
     */
    public function setViewPath($path)
    {
        $this->viewPath = rtrim($path, '/') . '/';
    }

    /**
     * Set layout path
     */
    public function setLayoutPath($path)
    {
        $this->layoutPath = rtrim($path, '/') . '/';
    }

    /**
     * Magic method to access view data
     */
    public function __get($key)
    {
        return $this->data[$key] ?? null;
    }

    /**
     * Magic method to set view data
     */
    public function __set($key, $value)
    {
        $this->data[$key] = $value;
    }

    /**
     * Magic method to check if view data exists
     */
    public function __isset($key)
    {
        return isset($this->data[$key]);
    }
}

?>
