<?php

namespace SouthSafari\Middleware;

use SouthSafari\Core\Session;

/**
 * Admin Middleware
 * 
 * Ensures user has admin privileges before accessing admin routes
 */
class AdminMiddleware
{
    private $session;

    public function __construct()
    {
        $this->session = new Session();
    }

    /**
     * Handle the middleware
     */
    public function handle($request, $response)
    {
        // First check if user is authenticated
        $authMiddleware = new AuthMiddleware();
        $authResult = $authMiddleware->handle($request, $response);
        
        if ($authResult !== true) {
            return $authResult;
        }

        // Check if user has admin role
        if (!$this->isAdmin()) {
            if ($this->isAjaxRequest($request)) {
                return $response->error('Admin privileges required', null, 403);
            }
            
            return $response->redirect('/?error=access_denied');
        }

        return true;
    }

    /**
     * Check if current user is admin
     */
    private function isAdmin()
    {
        return $this->session->get('user_role') === 'admin';
    }

    /**
     * Check if request is AJAX
     */
    private function isAjaxRequest($request)
    {
        return $request->isAjax();
    }
}

?>
