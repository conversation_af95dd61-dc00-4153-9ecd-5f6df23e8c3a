<?php

namespace SouthSafari\Middleware;

use SouthSafari\Core\Session;

/**
 * Authentication Middleware
 * 
 * Ensures user is authenticated before accessing protected routes
 */
class AuthMiddleware
{
    private $session;

    public function __construct()
    {
        $this->session = new Session();
    }

    /**
     * Handle the middleware
     */
    public function handle($request, $response)
    {
        // Check if user is logged in
        if (!$this->session->has('user_id')) {
            // If it's an AJAX request, return JSON error
            if ($this->isAjaxRequest($request)) {
                return $response->error('Authentication required', null, 401);
            }
            
            // Store intended URL for redirect after login
            $this->session->set('intended_url', $request->getUri());
            
            // Redirect to login page
            return $response->redirect('/login');
        }

        // Check if user account is still active
        if (!$this->isUserActive()) {
            $this->session->clear();
            
            if ($this->isAjaxRequest($request)) {
                return $response->error('Account is no longer active', null, 401);
            }
            
            return $response->redirect('/login?error=account_inactive');
        }

        return true;
    }

    /**
     * Check if request is AJAX
     */
    private function isAjaxRequest($request)
    {
        return $request->isAjax();
    }

    /**
     * Check if current user is still active
     */
    private function isUserActive()
    {
        $userId = $this->session->get('user_id');
        if (!$userId) {
            return false;
        }

        try {
            $db = \SouthSafari\Core\Database::getInstance();
            $user = $db->selectOne(
                "SELECT status FROM " . $db->table('users') . " WHERE id = ? AND deleted_at IS NULL",
                [$userId]
            );

            return $user && $user['status'] === 'active';
        } catch (\Exception $e) {
            error_log("Error checking user status: " . $e->getMessage());
            return false;
        }
    }
}

?>
