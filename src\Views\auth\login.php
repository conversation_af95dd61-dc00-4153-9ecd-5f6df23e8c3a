<?php
/**
 * Login Form Template
 */
?>

<h4 class="text-center mb-4">Welcome Back</h4>

<form method="POST" action="<?= $this->url('login') ?>" novalidate>
    <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
    
    <!-- Email -->
    <div class="mb-3">
        <label for="email" class="form-label">
            <i class="fas fa-envelope me-1"></i>
            Email Address
        </label>
        <input type="email" 
               class="form-control" 
               id="email" 
               name="email" 
               placeholder="Enter your email"
               required
               autocomplete="email">
    </div>
    
    <!-- Password -->
    <div class="mb-3">
        <label for="password" class="form-label">
            <i class="fas fa-lock me-1"></i>
            Password
        </label>
        <div class="position-relative">
            <input type="password" 
                   class="form-control" 
                   id="password" 
                   name="password" 
                   placeholder="Enter your password"
                   required
                   autocomplete="current-password">
            <button type="button" 
                    class="btn btn-link position-absolute end-0 top-50 translate-middle-y pe-3" 
                    onclick="togglePassword('password')"
                    tabindex="-1">
                <i class="fas fa-eye" id="password-toggle"></i>
            </button>
        </div>
    </div>
    
    <!-- Remember Me & Forgot Password -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div class="form-check">
            <input type="checkbox" class="form-check-input" id="remember" name="remember" value="1">
            <label class="form-check-label" for="remember">
                Remember me
            </label>
        </div>
        <a href="<?= $this->url('forgot-password') ?>" class="text-decoration-none">
            Forgot password?
        </a>
    </div>
    
    <!-- Submit Button -->
    <button type="submit" class="btn btn-primary w-100 mb-3">
        <i class="fas fa-sign-in-alt me-2"></i>
        Sign In
    </button>
</form>

<!-- Divider -->
<div class="divider">
    <span>Don't have an account?</span>
</div>

<!-- Register Link -->
<div class="auth-links">
    <a href="<?= $this->url('register') ?>" class="btn btn-outline-primary w-100">
        <i class="fas fa-user-plus me-2"></i>
        Create Account
    </a>
</div>

<!-- Additional Links -->
<div class="auth-links mt-3">
    <small class="text-muted">
        By signing in, you agree to our 
        <a href="<?= $this->url('terms') ?>">Terms of Service</a> and 
        <a href="<?= $this->url('privacy') ?>">Privacy Policy</a>
    </small>
</div>

<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const toggle = document.getElementById(fieldId + '-toggle');
    
    if (field.type === 'password') {
        field.type = 'text';
        toggle.classList.remove('fa-eye');
        toggle.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        toggle.classList.remove('fa-eye-slash');
        toggle.classList.add('fa-eye');
    }
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const email = document.getElementById('email').value.trim();
    const password = document.getElementById('password').value;
    
    if (!email || !password) {
        e.preventDefault();
        alert('Please fill in all required fields.');
        return false;
    }
    
    if (!isValidEmail(email)) {
        e.preventDefault();
        alert('Please enter a valid email address.');
        return false;
    }
});

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
</script>
