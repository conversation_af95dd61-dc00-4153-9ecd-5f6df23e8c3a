<?php
/**
 * Registration Form Template
 */
?>

<h4 class="text-center mb-4">Join South Safari</h4>
<p class="text-center text-muted mb-4">Create your developer account and start exploring partnership opportunities</p>

<form method="POST" action="<?= $this->url('register') ?>" novalidate>
    <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
    
    <!-- Name Fields -->
    <div class="row mb-3">
        <div class="col-md-6">
            <label for="first_name" class="form-label">
                <i class="fas fa-user me-1"></i>
                First Name
            </label>
            <input type="text" 
                   class="form-control" 
                   id="first_name" 
                   name="first_name" 
                   placeholder="First name"
                   required
                   autocomplete="given-name">
        </div>
        <div class="col-md-6">
            <label for="last_name" class="form-label">
                Last Name
            </label>
            <input type="text" 
                   class="form-control" 
                   id="last_name" 
                   name="last_name" 
                   placeholder="Last name"
                   required
                   autocomplete="family-name">
        </div>
    </div>
    
    <!-- Email -->
    <div class="mb-3">
        <label for="email" class="form-label">
            <i class="fas fa-envelope me-1"></i>
            Email Address
        </label>
        <input type="email" 
               class="form-control" 
               id="email" 
               name="email" 
               placeholder="Enter your email"
               required
               autocomplete="email">
    </div>
    
    <!-- Username -->
    <div class="mb-3">
        <label for="username" class="form-label">
            <i class="fas fa-at me-1"></i>
            Username
        </label>
        <input type="text" 
               class="form-control" 
               id="username" 
               name="username" 
               placeholder="Choose a username"
               required
               autocomplete="username"
               minlength="3"
               maxlength="50">
        <div class="form-text">3-50 characters, letters, numbers, and underscores only</div>
    </div>
    
    <!-- Password -->
    <div class="mb-3">
        <label for="password" class="form-label">
            <i class="fas fa-lock me-1"></i>
            Password
        </label>
        <div class="position-relative">
            <input type="password" 
                   class="form-control" 
                   id="password" 
                   name="password" 
                   placeholder="Create a password"
                   required
                   autocomplete="new-password"
                   minlength="8">
            <button type="button" 
                    class="btn btn-link position-absolute end-0 top-50 translate-middle-y pe-3" 
                    onclick="togglePassword('password')"
                    tabindex="-1">
                <i class="fas fa-eye" id="password-toggle"></i>
            </button>
        </div>
        <div class="form-text">Minimum 8 characters</div>
    </div>
    
    <!-- Confirm Password -->
    <div class="mb-3">
        <label for="password_confirm" class="form-label">
            <i class="fas fa-lock me-1"></i>
            Confirm Password
        </label>
        <div class="position-relative">
            <input type="password" 
                   class="form-control" 
                   id="password_confirm" 
                   name="password_confirm" 
                   placeholder="Confirm your password"
                   required
                   autocomplete="new-password">
            <button type="button" 
                    class="btn btn-link position-absolute end-0 top-50 translate-middle-y pe-3" 
                    onclick="togglePassword('password_confirm')"
                    tabindex="-1">
                <i class="fas fa-eye" id="password_confirm-toggle"></i>
            </button>
        </div>
    </div>
    
    <!-- Country -->
    <div class="mb-3">
        <label for="country" class="form-label">
            <i class="fas fa-globe me-1"></i>
            Country
        </label>
        <select class="form-select" id="country" name="country" required>
            <option value="">Select your country</option>
            <option value="Bangladesh">Bangladesh</option>
            <option value="India">India</option>
            <option value="Pakistan">Pakistan</option>
            <option value="Sri Lanka">Sri Lanka</option>
            <option value="Nepal">Nepal</option>
            <option value="Philippines">Philippines</option>
            <option value="Indonesia">Indonesia</option>
            <option value="Malaysia">Malaysia</option>
            <option value="Thailand">Thailand</option>
            <option value="Vietnam">Vietnam</option>
            <option value="Other">Other</option>
        </select>
    </div>
    
    <!-- Company Name (Optional) -->
    <div class="mb-3">
        <label for="company_name" class="form-label">
            <i class="fas fa-building me-1"></i>
            Company Name <small class="text-muted">(Optional)</small>
        </label>
        <input type="text" 
               class="form-control" 
               id="company_name" 
               name="company_name" 
               placeholder="Your company or organization"
               autocomplete="organization">
    </div>
    
    <!-- Terms Agreement -->
    <div class="mb-4">
        <div class="form-check">
            <input type="checkbox" class="form-check-input" id="terms" name="terms" required>
            <label class="form-check-label" for="terms">
                I agree to the 
                <a href="<?= $this->url('terms') ?>" target="_blank">Terms of Service</a> 
                and 
                <a href="<?= $this->url('privacy') ?>" target="_blank">Privacy Policy</a>
            </label>
        </div>
    </div>
    
    <!-- Submit Button -->
    <button type="submit" class="btn btn-primary w-100 mb-3">
        <i class="fas fa-user-plus me-2"></i>
        Create Account
    </button>
</form>

<!-- Divider -->
<div class="divider">
    <span>Already have an account?</span>
</div>

<!-- Login Link -->
<div class="auth-links">
    <a href="<?= $this->url('login') ?>" class="btn btn-outline-primary w-100">
        <i class="fas fa-sign-in-alt me-2"></i>
        Sign In
    </a>
</div>

<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const toggle = document.getElementById(fieldId + '-toggle');
    
    if (field.type === 'password') {
        field.type = 'text';
        toggle.classList.remove('fa-eye');
        toggle.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        toggle.classList.remove('fa-eye-slash');
        toggle.classList.add('fa-eye');
    }
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const firstName = document.getElementById('first_name').value.trim();
    const lastName = document.getElementById('last_name').value.trim();
    const email = document.getElementById('email').value.trim();
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;
    const passwordConfirm = document.getElementById('password_confirm').value;
    const country = document.getElementById('country').value;
    const terms = document.getElementById('terms').checked;
    
    // Check required fields
    if (!firstName || !lastName || !email || !username || !password || !passwordConfirm || !country) {
        e.preventDefault();
        alert('Please fill in all required fields.');
        return false;
    }
    
    // Validate email
    if (!isValidEmail(email)) {
        e.preventDefault();
        alert('Please enter a valid email address.');
        return false;
    }
    
    // Validate username
    if (username.length < 3 || username.length > 50) {
        e.preventDefault();
        alert('Username must be between 3 and 50 characters.');
        return false;
    }
    
    if (!/^[a-zA-Z0-9_]+$/.test(username)) {
        e.preventDefault();
        alert('Username can only contain letters, numbers, and underscores.');
        return false;
    }
    
    // Validate password
    if (password.length < 8) {
        e.preventDefault();
        alert('Password must be at least 8 characters long.');
        return false;
    }
    
    // Check password confirmation
    if (password !== passwordConfirm) {
        e.preventDefault();
        alert('Passwords do not match.');
        return false;
    }
    
    // Check terms agreement
    if (!terms) {
        e.preventDefault();
        alert('Please agree to the Terms of Service and Privacy Policy.');
        return false;
    }
});

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Real-time password confirmation validation
document.getElementById('password_confirm').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const passwordConfirm = this.value;
    
    if (passwordConfirm && password !== passwordConfirm) {
        this.setCustomValidity('Passwords do not match');
        this.classList.add('is-invalid');
    } else {
        this.setCustomValidity('');
        this.classList.remove('is-invalid');
    }
});
</script>
