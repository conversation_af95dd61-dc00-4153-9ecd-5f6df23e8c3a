<?php
/**
 * User Dashboard Template
 */
?>

<!-- Dashboard Header -->
<section class="dashboard-header bg-light py-4">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="h3 mb-2">Welcome back, <?= $this->escape($user['first_name']) ?>!</h1>
                <p class="text-muted mb-0">
                    Here's what's happening with your partnership journey
                </p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <div class="profile-completion">
                    <small class="text-muted">Profile Completion</small>
                    <div class="progress mt-1" style="height: 8px;">
                        <?php 
                        $completion = isset($stats['profile_completion']) ? $stats['profile_completion'] : 50;
                        ?>
                        <div class="progress-bar bg-success" style="width: <?= $completion ?>%"></div>
                    </div>
                    <small class="text-success"><?= $completion ?>% Complete</small>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Dashboard Stats -->
<section class="dashboard-stats py-4">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="stat-icon mb-3">
                            <i class="fas fa-file-alt fa-2x text-primary"></i>
                        </div>
                        <h3 class="h4 mb-1"><?= number_format($stats['total_applications']) ?></h3>
                        <p class="text-muted mb-0">Total Applications</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="stat-icon mb-3">
                            <i class="fas fa-clock fa-2x text-warning"></i>
                        </div>
                        <h3 class="h4 mb-1"><?= number_format($stats['pending_applications']) ?></h3>
                        <p class="text-muted mb-0">Pending Applications</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="stat-icon mb-3">
                            <i class="fas fa-handshake fa-2x text-success"></i>
                        </div>
                        <h3 class="h4 mb-1"><?= number_format($stats['active_partnerships']) ?></h3>
                        <p class="text-muted mb-0">Active Partnerships</p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center">
                        <div class="stat-icon mb-3">
                            <i class="fas fa-envelope fa-2x text-info"></i>
                        </div>
                        <h3 class="h4 mb-1"><?= number_format($stats['unread_messages']) ?></h3>
                        <p class="text-muted mb-0">Unread Messages</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Dashboard Content -->
<section class="dashboard-content py-4">
    <div class="container">
        <div class="row">
            <!-- Recent Applications -->
            <div class="col-lg-6 mb-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-white border-bottom">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-file-alt me-2 text-primary"></i>
                                Recent Applications
                            </h5>
                            <a href="<?= $this->url('dashboard/applications') ?>" class="btn btn-sm btn-outline-primary">
                                View All
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_applications)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-file-alt fa-3x text-muted opacity-50 mb-3"></i>
                                <p class="text-muted">No applications yet</p>
                                <a href="<?= $this->url('projects') ?>" class="btn btn-primary btn-sm">
                                    Browse Projects
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="list-group list-group-flush">
                                <?php foreach ($recent_applications as $application): ?>
                                    <div class="list-group-item px-0">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="mb-1">
                                                    <a href="<?= $this->url('projects/' . $application['project_slug']) ?>" 
                                                       class="text-decoration-none">
                                                        <?= $this->escape($application['project_title']) ?>
                                                    </a>
                                                </h6>
                                                <small class="text-muted">
                                                    Applied <?= $this->formatDate($application['created_at']) ?>
                                                </small>
                                            </div>
                                            <span class="badge bg-<?= $this->getStatusColor($application['status']) ?>">
                                                <?= ucfirst($application['status']) ?>
                                            </span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Recommended Projects -->
            <div class="col-lg-6 mb-4">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-header bg-white border-bottom">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-lightbulb me-2 text-warning"></i>
                                Recommended for You
                            </h5>
                            <a href="<?= $this->url('projects') ?>" class="btn btn-sm btn-outline-primary">
                                View All
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recommended_projects)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-lightbulb fa-3x text-muted opacity-50 mb-3"></i>
                                <p class="text-muted">No recommendations available</p>
                                <small class="text-muted">Complete your profile to get better recommendations</small>
                            </div>
                        <?php else: ?>
                            <?php foreach ($recommended_projects as $project): ?>
                                <div class="mb-3 pb-3 border-bottom">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <span class="badge bg-primary"><?= $this->escape($project['category']) ?></span>
                                        <small class="text-muted"><?= $this->formatDate($project['created_at']) ?></small>
                                    </div>
                                    <h6 class="mb-2">
                                        <a href="<?= $this->url('projects/' . $project['slug']) ?>" 
                                           class="text-decoration-none">
                                            <?= $this->escape($project['title']) ?>
                                        </a>
                                    </h6>
                                    <p class="text-muted small mb-2">
                                        <?= $this->truncate($this->escape($project['description']), 100) ?>
                                    </p>
                                    <small class="text-muted">
                                        by <?= $this->escape($project['company_name'] ?: $project['first_name'] . ' ' . $project['last_name']) ?>
                                    </small>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Recent Messages -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-envelope me-2 text-info"></i>
                                Recent Messages
                            </h5>
                            <a href="<?= $this->url('dashboard/messages') ?>" class="btn btn-sm btn-outline-primary">
                                View All
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_messages)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-envelope fa-3x text-muted opacity-50 mb-3"></i>
                                <p class="text-muted">No messages yet</p>
                                <small class="text-muted">Messages from project owners will appear here</small>
                            </div>
                        <?php else: ?>
                            <div class="list-group list-group-flush">
                                <?php foreach ($recent_messages as $message): ?>
                                    <div class="list-group-item px-0">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div class="flex-grow-1">
                                                <div class="d-flex justify-content-between align-items-center mb-1">
                                                    <h6 class="mb-0">
                                                        <?= $this->escape($message['first_name'] . ' ' . $message['last_name']) ?>
                                                    </h6>
                                                    <small class="text-muted">
                                                        <?= $this->formatDate($message['created_at']) ?>
                                                    </small>
                                                </div>
                                                <p class="mb-1"><?= $this->escape($message['subject'] ?: 'No Subject') ?></p>
                                                <small class="text-muted">
                                                    <?= $this->truncate($this->escape($message['message']), 100) ?>
                                                </small>
                                            </div>
                                            <?php if (!$message['is_read']): ?>
                                                <span class="badge bg-primary ms-2">New</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Quick Actions -->
<section class="quick-actions py-4 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h5 class="mb-3">Quick Actions</h5>
                <div class="d-flex flex-wrap gap-2">
                    <a href="<?= $this->url('projects') ?>" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>Browse Projects
                    </a>
                    <a href="<?= $this->url('dashboard/profile') ?>" class="btn btn-outline-primary">
                        <i class="fas fa-user me-2"></i>Update Profile
                    </a>
                    <a href="<?= $this->url('dashboard/applications') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-file-alt me-2"></i>My Applications
                    </a>
                    <a href="<?= $this->url('dashboard/messages') ?>" class="btn btn-outline-info">
                        <i class="fas fa-envelope me-2"></i>Messages
                        <?php if ($stats['unread_messages'] > 0): ?>
                            <span class="badge bg-danger ms-1"><?= $stats['unread_messages'] ?></span>
                        <?php endif; ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.dashboard-stats .card {
    transition: transform 0.3s ease;
}

.dashboard-stats .card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    opacity: 0.8;
}

.progress {
    background-color: #e9ecef;
}

.list-group-item {
    border: none;
    border-bottom: 1px solid #e9ecef;
}

.list-group-item:last-child {
    border-bottom: none;
}

.quick-actions .btn {
    margin-bottom: 0.5rem;
}

@media (max-width: 768px) {
    .dashboard-header .col-lg-4 {
        margin-top: 1rem;
    }
    
    .quick-actions .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}
</style>

<?php
// Helper function for status colors
function getStatusColor($status) {
    switch ($status) {
        case 'pending': return 'warning';
        case 'under_review': return 'info';
        case 'shortlisted': return 'primary';
        case 'accepted': return 'success';
        case 'rejected': return 'danger';
        default: return 'secondary';
    }
}
$this->getStatusColor = 'getStatusColor';
?>
