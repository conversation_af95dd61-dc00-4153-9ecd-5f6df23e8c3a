<?php
/**
 * 404 Error Page Template
 */
?>

<div class="error-page">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6 text-center">
                <div class="error-content">
                    <!-- Error Icon -->
                    <div class="error-icon mb-4">
                        <i class="fas fa-search display-1 text-muted"></i>
                    </div>
                    
                    <!-- Error Code -->
                    <h1 class="error-code display-1 fw-bold text-primary mb-3">404</h1>
                    
                    <!-- Error Message -->
                    <h2 class="error-title h3 mb-3">Page Not Found</h2>
                    <p class="error-description lead text-muted mb-4">
                        <?= isset($message) ? $this->escape($message) : 'The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.' ?>
                    </p>
                    
                    <!-- Action Buttons -->
                    <div class="error-actions">
                        <a href="<?= $this->url() ?>" class="btn btn-primary btn-lg me-3">
                            <i class="fas fa-home me-2"></i>
                            Go Home
                        </a>
                        <button onclick="history.back()" class="btn btn-outline-secondary btn-lg">
                            <i class="fas fa-arrow-left me-2"></i>
                            Go Back
                        </button>
                    </div>
                    
                    <!-- Search Box -->
                    <div class="error-search mt-5">
                        <h5 class="mb-3">Or search for what you need:</h5>
                        <form action="<?= $this->url('search') ?>" method="GET" class="d-flex justify-content-center">
                            <div class="input-group" style="max-width: 400px;">
                                <input type="text" 
                                       name="q" 
                                       class="form-control" 
                                       placeholder="Search projects, categories..."
                                       aria-label="Search">
                                <button class="btn btn-outline-primary" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <!-- Helpful Links -->
                    <div class="helpful-links mt-5">
                        <h6 class="text-muted mb-3">You might be looking for:</h6>
                        <div class="row">
                            <div class="col-md-6 mb-2">
                                <a href="<?= $this->url('projects') ?>" class="text-decoration-none">
                                    <i class="fas fa-briefcase me-2 text-primary"></i>
                                    Browse Projects
                                </a>
                            </div>
                            <div class="col-md-6 mb-2">
                                <a href="<?= $this->url('categories') ?>" class="text-decoration-none">
                                    <i class="fas fa-tags me-2 text-primary"></i>
                                    View Categories
                                </a>
                            </div>
                            <div class="col-md-6 mb-2">
                                <a href="<?= $this->url('how-it-works') ?>" class="text-decoration-none">
                                    <i class="fas fa-question-circle me-2 text-primary"></i>
                                    How It Works
                                </a>
                            </div>
                            <div class="col-md-6 mb-2">
                                <a href="<?= $this->url('contact') ?>" class="text-decoration-none">
                                    <i class="fas fa-envelope me-2 text-primary"></i>
                                    Contact Support
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-page {
    min-height: 80vh;
    display: flex;
    align-items: center;
    padding: 2rem 0;
}

.error-content {
    animation: fadeInUp 0.6s ease-out;
}

.error-code {
    color: var(--primary-color);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.error-icon {
    opacity: 0.3;
}

.helpful-links a {
    color: #6c757d;
    transition: color 0.3s ease;
}

.helpful-links a:hover {
    color: var(--primary-color);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@media (max-width: 768px) {
    .error-code {
        font-size: 4rem;
    }
    
    .error-actions .btn {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
    }
    
    .error-actions .btn:last-child {
        margin-bottom: 0;
    }
}
</style>
