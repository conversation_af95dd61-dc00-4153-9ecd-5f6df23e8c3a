<?php
/**
 * 500 Error Page Template
 */
?>

<div class="error-page">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6 text-center">
                <div class="error-content">
                    <!-- Error Icon -->
                    <div class="error-icon mb-4">
                        <i class="fas fa-exclamation-triangle display-1 text-warning"></i>
                    </div>
                    
                    <!-- Error Code -->
                    <h1 class="error-code display-1 fw-bold text-danger mb-3">500</h1>
                    
                    <!-- Error Message -->
                    <h2 class="error-title h3 mb-3">Internal Server Error</h2>
                    <p class="error-description lead text-muted mb-4">
                        <?= isset($message) ? $this->escape($message) : 'Something went wrong on our end. We\'re working to fix this issue.' ?>
                    </p>
                    
                    <!-- Action Buttons -->
                    <div class="error-actions">
                        <a href="<?= $this->url() ?>" class="btn btn-primary btn-lg me-3">
                            <i class="fas fa-home me-2"></i>
                            Go Home
                        </a>
                        <button onclick="location.reload()" class="btn btn-outline-secondary btn-lg">
                            <i class="fas fa-redo me-2"></i>
                            Try Again
                        </button>
                    </div>
                    
                    <!-- Support Information -->
                    <div class="error-support mt-5">
                        <div class="alert alert-light border">
                            <h6 class="alert-heading">
                                <i class="fas fa-life-ring me-2"></i>
                                Need Help?
                            </h6>
                            <p class="mb-2">
                                If this problem persists, please contact our support team with the following information:
                            </p>
                            <ul class="list-unstyled mb-0">
                                <li><strong>Time:</strong> <?= date('Y-m-d H:i:s T') ?></li>
                                <li><strong>Page:</strong> <?= $this->escape($_SERVER['REQUEST_URI'] ?? '/') ?></li>
                                <li><strong>Error ID:</strong> <?= uniqid() ?></li>
                            </ul>
                        </div>
                        
                        <a href="<?= $this->url('contact') ?>" class="btn btn-outline-primary">
                            <i class="fas fa-envelope me-2"></i>
                            Contact Support
                        </a>
                    </div>
                    
                    <!-- Status Information -->
                    <div class="status-info mt-4">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Check our <a href="<?= $this->url('status') ?>">status page</a> for any ongoing issues
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-page {
    min-height: 80vh;
    display: flex;
    align-items: center;
    padding: 2rem 0;
}

.error-content {
    animation: fadeInUp 0.6s ease-out;
}

.error-code {
    color: var(--danger-color);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.error-icon {
    opacity: 0.7;
}

.error-support .alert {
    text-align: left;
    max-width: 500px;
    margin: 0 auto;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@media (max-width: 768px) {
    .error-code {
        font-size: 4rem;
    }
    
    .error-actions .btn {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
    }
    
    .error-actions .btn:last-child {
        margin-bottom: 0;
    }
}
</style>
