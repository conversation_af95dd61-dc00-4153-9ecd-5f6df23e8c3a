<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    
    <!-- SEO Meta Tags -->
    <title><?= isset($title) ? $this->escape($title) : 'South Safari - Partnership Platform' ?></title>
    <meta name="description" content="<?= isset($meta_description) ? $this->escape($meta_description) : 'Connect South African opportunities with global talent through our partnership platform.' ?>">
    <meta name="keywords" content="south africa, partnerships, developers, business opportunities, software development">
    <meta name="author" content="South Safari Platform">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?= isset($title) ? $this->escape($title) : 'South Safari - Partnership Platform' ?>">
    <meta property="og:description" content="<?= isset($meta_description) ? $this->escape($meta_description) : 'Connect South African opportunities with global talent.' ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?= $this->url() ?>">
    <meta property="og:site_name" content="South Safari">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?= isset($title) ? $this->escape($title) : 'South Safari - Partnership Platform' ?>">
    <meta name="twitter:description" content="<?= isset($meta_description) ? $this->escape($meta_description) : 'Connect South African opportunities with global talent.' ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= $this->asset('images/favicon.ico') ?>">
    <link rel="apple-touch-icon" href="<?= $this->asset('images/apple-touch-icon.png') ?>">
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="<?= $this->asset('css/main.css') ?>" rel="stylesheet">
    
    <!-- Additional CSS -->
    <?php if (isset($additional_css)): ?>
        <?php foreach ($additional_css as $css): ?>
            <link href="<?= $this->asset($css) ?>" rel="stylesheet">
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- CSRF Token -->
    <meta name="csrf-token" content="<?= $csrf_token ?>">
</head>
<body class="<?= isset($page_class) ? $this->escape($page_class) : '' ?>">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container">
            <!-- Brand -->
            <a class="navbar-brand fw-bold" href="<?= $this->url() ?>">
                <i class="fas fa-mountain me-2"></i>
                <?= $app_name ?>
            </a>
            
            <!-- Mobile Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <!-- Navigation Links -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?= $this->url() ?>">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= $this->url('projects') ?>">Projects</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= $this->url('categories') ?>">Categories</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= $this->url('how-it-works') ?>">How It Works</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= $this->url('about') ?>">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?= $this->url('contact') ?>">Contact</a>
                    </li>
                </ul>
                
                <!-- User Menu -->
                <ul class="navbar-nav">
                    <?php if ($is_logged_in): ?>
                        <!-- Logged In User Menu -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle me-1"></i>
                                <?= $this->escape($current_user['first_name'] ?? 'User') ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <?php if ($is_admin): ?>
                                    <li><a class="dropdown-item" href="<?= $this->url('admin') ?>">
                                        <i class="fas fa-tachometer-alt me-2"></i>Admin Dashboard
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                <?php else: ?>
                                    <li><a class="dropdown-item" href="<?= $this->url('dashboard') ?>">
                                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                    </a></li>
                                <?php endif; ?>
                                <li><a class="dropdown-item" href="<?= $this->url('dashboard/profile') ?>">
                                    <i class="fas fa-user me-2"></i>Profile
                                </a></li>
                                <li><a class="dropdown-item" href="<?= $this->url('dashboard/applications') ?>">
                                    <i class="fas fa-file-alt me-2"></i>My Applications
                                </a></li>
                                <li><a class="dropdown-item" href="<?= $this->url('dashboard/messages') ?>">
                                    <i class="fas fa-envelope me-2"></i>Messages
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form method="POST" action="<?= $this->url('logout') ?>" class="d-inline">
                                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                                        <button type="submit" class="dropdown-item text-danger">
                                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <!-- Guest User Menu -->
                        <li class="nav-item">
                            <a class="nav-link" href="<?= $this->url('login') ?>">
                                <i class="fas fa-sign-in-alt me-1"></i>Login
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link btn btn-outline-light ms-2 px-3" href="<?= $this->url('register') ?>">
                                <i class="fas fa-user-plus me-1"></i>Register
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="main-content" style="margin-top: 76px;">
        <!-- Flash Messages -->
        <?php if (!empty($flash_messages)): ?>
            <div class="container mt-3">
                <?= $this->flashMessages($flash_messages) ?>
            </div>
        <?php endif; ?>
        
        <!-- Page Content -->
        <?= $content ?>
    </main>
    
    <!-- Footer -->
    <footer class="bg-dark text-light py-5 mt-5">
        <div class="container">
            <div class="row">
                <!-- About Section -->
                <div class="col-lg-4 mb-4">
                    <h5 class="fw-bold mb-3">
                        <i class="fas fa-mountain me-2"></i>
                        <?= $app_name ?>
                    </h5>
                    <p class="text-light-50">
                        Connecting South African opportunities with global talent. 
                        Build partnerships, grow your business, and succeed together.
                    </p>
                    <div class="social-links">
                        <a href="#" class="text-light me-3"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                
                <!-- Quick Links -->
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3">Quick Links</h6>
                    <ul class="list-unstyled">
                        <li><a href="<?= $this->url() ?>" class="text-light-50 text-decoration-none">Home</a></li>
                        <li><a href="<?= $this->url('projects') ?>" class="text-light-50 text-decoration-none">Projects</a></li>
                        <li><a href="<?= $this->url('categories') ?>" class="text-light-50 text-decoration-none">Categories</a></li>
                        <li><a href="<?= $this->url('how-it-works') ?>" class="text-light-50 text-decoration-none">How It Works</a></li>
                        <li><a href="<?= $this->url('about') ?>" class="text-light-50 text-decoration-none">About</a></li>
                    </ul>
                </div>
                
                <!-- Support -->
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="fw-bold mb-3">Support</h6>
                    <ul class="list-unstyled">
                        <li><a href="<?= $this->url('contact') ?>" class="text-light-50 text-decoration-none">Contact Us</a></li>
                        <li><a href="<?= $this->url('help') ?>" class="text-light-50 text-decoration-none">Help Center</a></li>
                        <li><a href="<?= $this->url('faq') ?>" class="text-light-50 text-decoration-none">FAQ</a></li>
                        <li><a href="<?= $this->url('privacy') ?>" class="text-light-50 text-decoration-none">Privacy Policy</a></li>
                        <li><a href="<?= $this->url('terms') ?>" class="text-light-50 text-decoration-none">Terms of Service</a></li>
                    </ul>
                </div>
                
                <!-- Contact Info -->
                <div class="col-lg-4 mb-4">
                    <h6 class="fw-bold mb-3">Contact Information</h6>
                    <div class="contact-info">
                        <p class="text-light-50 mb-2">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            Cape Town, South Africa
                        </p>
                        <p class="text-light-50 mb-2">
                            <i class="fas fa-envelope me-2"></i>
                            <EMAIL>
                        </p>
                        <p class="text-light-50 mb-2">
                            <i class="fas fa-phone me-2"></i>
                            +27 (0) 21 XXX XXXX
                        </p>
                    </div>
                </div>
            </div>
            
            <hr class="my-4 border-secondary">
            
            <!-- Copyright -->
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-light-50 mb-0">
                        &copy; <?= date('Y') ?> <?= $app_name ?>. All rights reserved.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-light-50 mb-0">
                        <?= defined('CUSTOM_FOOTER_TEXT') ? CUSTOM_FOOTER_TEXT : 'Connecting opportunities across continents' ?>
                    </p>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?= $this->asset('js/main.js') ?>"></script>
    
    <!-- Additional JavaScript -->
    <?php if (isset($additional_js)): ?>
        <?php foreach ($additional_js as $js): ?>
            <script src="<?= $this->asset($js) ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- CSRF Token for AJAX -->
    <script>
        window.csrfToken = '<?= $csrf_token ?>';
        
        // Set CSRF token for all AJAX requests
        if (typeof $ !== 'undefined') {
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': window.csrfToken
                }
            });
        }
    </script>
    
    <!-- Custom JavaScript -->
    <?php if (isset($custom_js)): ?>
        <script><?= $custom_js ?></script>
    <?php endif; ?>
</body>
</html>
