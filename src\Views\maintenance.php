<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Maintenance Mode - South Safari</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .maintenance-container {
            max-width: 600px;
            margin: 0 auto;
            text-align: center;
            color: white;
        }
        
        .maintenance-icon {
            font-size: 5rem;
            margin-bottom: 2rem;
            opacity: 0.8;
            animation: pulse 2s infinite;
        }
        
        .maintenance-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 3rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .brand-logo {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        .progress-bar {
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            overflow: hidden;
            margin: 2rem 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #48bb78, #38a169);
            border-radius: 3px;
            animation: progress 3s ease-in-out infinite;
        }
        
        .social-links a {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.5rem;
            margin: 0 1rem;
            transition: color 0.3s ease;
        }
        
        .social-links a:hover {
            color: white;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        @keyframes progress {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }
        
        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }
        
        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }
        
        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(120deg); }
            66% { transform: translateY(20px) rotate(240deg); }
        }
    </style>
</head>
<body>
    <!-- Floating Shapes -->
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>
    
    <div class="container">
        <div class="maintenance-container">
            <div class="maintenance-card">
                <!-- Brand -->
                <div class="brand-logo">
                    <i class="fas fa-mountain me-2"></i>
                    South Safari
                </div>
                
                <!-- Maintenance Icon -->
                <div class="maintenance-icon">
                    <i class="fas fa-tools"></i>
                </div>
                
                <!-- Main Message -->
                <h1 class="h2 mb-3">We'll Be Back Soon!</h1>
                <p class="lead mb-4">
                    We're currently performing scheduled maintenance to improve your experience. 
                    Our platform will be back online shortly.
                </p>
                
                <!-- Progress Bar -->
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
                
                <!-- Details -->
                <div class="maintenance-details">
                    <h5 class="mb-3">What's Happening?</h5>
                    <ul class="list-unstyled text-start" style="max-width: 400px; margin: 0 auto;">
                        <li class="mb-2">
                            <i class="fas fa-check-circle me-2 text-success"></i>
                            Database optimization
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check-circle me-2 text-success"></i>
                            Security updates
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-spinner fa-spin me-2 text-warning"></i>
                            Performance improvements
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-clock me-2 text-info"></i>
                            New features deployment
                        </li>
                    </ul>
                </div>
                
                <!-- Estimated Time -->
                <div class="estimated-time mt-4 p-3" style="background: rgba(255, 255, 255, 0.1); border-radius: 10px;">
                    <h6 class="mb-2">
                        <i class="fas fa-clock me-2"></i>
                        Estimated Completion
                    </h6>
                    <div class="h5 mb-0" id="countdown">
                        Calculating...
                    </div>
                </div>
                
                <!-- Contact Information -->
                <div class="contact-info mt-4">
                    <p class="mb-2">
                        <strong>Need immediate assistance?</strong>
                    </p>
                    <p class="mb-3">
                        <i class="fas fa-envelope me-2"></i>
                        <a href="mailto:<EMAIL>" class="text-white"><EMAIL></a>
                    </p>
                    
                    <!-- Social Links -->
                    <div class="social-links">
                        <a href="#" title="Facebook"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" title="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" title="LinkedIn"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#" title="Instagram"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
                
                <!-- Auto Refresh Notice -->
                <div class="auto-refresh mt-4">
                    <small class="text-white-50">
                        <i class="fas fa-sync-alt me-1"></i>
                        This page will automatically refresh every 30 seconds
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Auto refresh every 30 seconds
        setTimeout(function() {
            location.reload();
        }, 30000);
        
        // Countdown timer (example: 2 hours from now)
        function updateCountdown() {
            const now = new Date().getTime();
            const targetTime = now + (2 * 60 * 60 * 1000); // 2 hours from now
            
            const interval = setInterval(function() {
                const currentTime = new Date().getTime();
                const timeLeft = targetTime - currentTime;
                
                if (timeLeft > 0) {
                    const hours = Math.floor(timeLeft / (1000 * 60 * 60));
                    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
                    const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
                    
                    document.getElementById('countdown').innerHTML = 
                        `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                } else {
                    document.getElementById('countdown').innerHTML = 'Almost ready...';
                    clearInterval(interval);
                    // Refresh page when countdown reaches zero
                    setTimeout(function() {
                        location.reload();
                    }, 5000);
                }
            }, 1000);
        }
        
        // Start countdown
        updateCountdown();
        
        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            // Animate maintenance icon on click
            document.querySelector('.maintenance-icon').addEventListener('click', function() {
                this.style.animation = 'none';
                setTimeout(() => {
                    this.style.animation = 'pulse 2s infinite';
                }, 100);
            });
        });
    </script>
</body>
</html>
