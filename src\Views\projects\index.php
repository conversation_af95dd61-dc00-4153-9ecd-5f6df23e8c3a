<?php
/**
 * Projects Listing Template
 */
?>

<!-- <PERSON> Header -->
<section class="page-header bg-light py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <h1 class="display-5 fw-bold mb-3">Partnership Opportunities</h1>
                <p class="lead text-muted">
                    Discover exciting partnership opportunities with South African businesses
                </p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <div class="stats-summary">
                    <div class="stat-item d-inline-block me-4">
                        <div class="stat-number h4 mb-0 text-primary"><?= count($projects) ?></div>
                        <div class="stat-label small text-muted">Projects Found</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Filters and Search -->
<section class="filters-section py-4 bg-white border-bottom">
    <div class="container">
        <form method="GET" action="<?= $this->url('projects') ?>" class="row g-3 align-items-end">
            <!-- Search -->
            <div class="col-lg-4">
                <label for="search" class="form-label">Search Projects</label>
                <div class="input-group">
                    <input type="text" 
                           class="form-control" 
                           id="search" 
                           name="search" 
                           placeholder="Search by title, description..."
                           value="<?= $this->escape($current_search) ?>">
                    <button class="btn btn-outline-primary" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
            
            <!-- Category Filter -->
            <div class="col-lg-3">
                <label for="category" class="form-label">Category</label>
                <select class="form-select" id="category" name="category">
                    <option value="">All Categories</option>
                    <?php foreach ($categories as $category): ?>
                        <option value="<?= $this->escape($category['slug']) ?>" 
                                <?= $current_category === $category['slug'] ? 'selected' : '' ?>>
                            <?= $this->escape($category['name']) ?> 
                            (<?= $category['project_count'] ?>)
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <!-- Sort -->
            <div class="col-lg-3">
                <label for="sort" class="form-label">Sort By</label>
                <select class="form-select" id="sort" name="sort">
                    <option value="latest" <?= $current_sort === 'latest' ? 'selected' : '' ?>>Latest</option>
                    <option value="oldest" <?= $current_sort === 'oldest' ? 'selected' : '' ?>>Oldest</option>
                    <option value="popular" <?= $current_sort === 'popular' ? 'selected' : '' ?>>Most Popular</option>
                    <option value="applications" <?= $current_sort === 'applications' ? 'selected' : '' ?>>Most Applications</option>
                    <option value="featured" <?= $current_sort === 'featured' ? 'selected' : '' ?>>Featured First</option>
                    <option value="title" <?= $current_sort === 'title' ? 'selected' : '' ?>>Title A-Z</option>
                </select>
            </div>
            
            <!-- Filter Button -->
            <div class="col-lg-2">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-filter me-1"></i>
                    Filter
                </button>
            </div>
        </form>
        
        <!-- Active Filters -->
        <?php if ($current_search || $current_category): ?>
            <div class="active-filters mt-3">
                <span class="text-muted me-2">Active filters:</span>
                <?php if ($current_search): ?>
                    <span class="badge bg-primary me-2">
                        Search: "<?= $this->escape($current_search) ?>"
                        <a href="<?= $this->url('projects') ?>?category=<?= urlencode($current_category) ?>&sort=<?= urlencode($current_sort) ?>" 
                           class="text-white ms-1">×</a>
                    </span>
                <?php endif; ?>
                <?php if ($current_category): ?>
                    <span class="badge bg-primary me-2">
                        Category: <?= $this->escape($current_category) ?>
                        <a href="<?= $this->url('projects') ?>?search=<?= urlencode($current_search) ?>&sort=<?= urlencode($current_sort) ?>" 
                           class="text-white ms-1">×</a>
                    </span>
                <?php endif; ?>
                <a href="<?= $this->url('projects') ?>" class="btn btn-sm btn-outline-secondary">
                    Clear All
                </a>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Projects Grid -->
<section class="projects-grid py-5">
    <div class="container">
        <?php if (empty($projects)): ?>
            <!-- No Projects Found -->
            <div class="text-center py-5">
                <div class="mb-4">
                    <i class="fas fa-search display-1 text-muted opacity-50"></i>
                </div>
                <h3 class="h4 mb-3">No Projects Found</h3>
                <p class="text-muted mb-4">
                    <?php if ($current_search || $current_category): ?>
                        No projects match your current filters. Try adjusting your search criteria.
                    <?php else: ?>
                        There are currently no partnership opportunities available.
                    <?php endif; ?>
                </p>
                <?php if ($current_search || $current_category): ?>
                    <a href="<?= $this->url('projects') ?>" class="btn btn-primary">
                        <i class="fas fa-refresh me-2"></i>
                        View All Projects
                    </a>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <!-- Projects List -->
            <div class="row">
                <?php foreach ($projects as $project): ?>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card h-100 project-card">
                            <?php if ($project['featured']): ?>
                                <div class="card-header bg-warning text-dark py-2">
                                    <small class="fw-bold">
                                        <i class="fas fa-star me-1"></i>
                                        Featured Project
                                    </small>
                                </div>
                            <?php endif; ?>
                            
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start mb-3">
                                    <span class="badge bg-primary"><?= $this->escape($project['category']) ?></span>
                                    <small class="text-muted"><?= $this->formatDate($project['created_at']) ?></small>
                                </div>
                                
                                <h5 class="card-title">
                                    <a href="<?= $this->url('projects/' . $project['slug']) ?>" 
                                       class="text-decoration-none">
                                        <?= $this->escape($project['title']) ?>
                                    </a>
                                </h5>
                                
                                <p class="card-text text-muted">
                                    <?= $this->truncate($this->escape($project['description']), 120) ?>
                                </p>
                                
                                <div class="project-meta mb-3">
                                    <div class="row text-sm text-muted">
                                        <div class="col-6">
                                            <i class="fas fa-clock me-1"></i>
                                            <?= $this->escape($project['timeline'] ?? 'Flexible') ?>
                                        </div>
                                        <div class="col-6">
                                            <i class="fas fa-eye me-1"></i>
                                            <?= number_format($project['views_count']) ?> views
                                        </div>
                                    </div>
                                    <div class="row text-sm text-muted mt-1">
                                        <div class="col-6">
                                            <i class="fas fa-users me-1"></i>
                                            <?= number_format($project['application_count'] ?? 0) ?> applications
                                        </div>
                                        <div class="col-6">
                                            <i class="fas fa-dollar-sign me-1"></i>
                                            <?= $this->escape($project['compensation_type']) ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card-footer bg-transparent">
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        by <?= $this->escape($project['company_name'] ?: $project['first_name'] . ' ' . $project['last_name']) ?>
                                    </small>
                                    <a href="<?= $this->url('projects/' . $project['slug']) ?>" 
                                       class="btn btn-outline-primary btn-sm">
                                        View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <!-- Pagination -->
            <?php if ($pagination['total_pages'] > 1): ?>
                <div class="d-flex justify-content-center mt-5">
                    <?= $this->pagination($pagination, $this->url('projects') . '?' . http_build_query([
                        'search' => $current_search,
                        'category' => $current_category,
                        'sort' => $current_sort
                    ])) ?>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</section>

<!-- Call to Action -->
<?php if (!$is_logged_in): ?>
<section class="cta-section bg-primary text-white py-5">
    <div class="container text-center">
        <h3 class="mb-3">Ready to Start Your Partnership Journey?</h3>
        <p class="lead mb-4">
            Join thousands of developers who have found success through South Safari partnerships.
        </p>
        <a href="<?= $this->url('register') ?>" class="btn btn-light btn-lg">
            <i class="fas fa-user-plus me-2"></i>
            Register as Developer
        </a>
    </div>
</section>
<?php endif; ?>

<style>
.project-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid #e9ecef;
}

.project-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    border-color: var(--primary-color);
}

.project-meta {
    font-size: 0.875rem;
}

.active-filters .badge {
    font-size: 0.875rem;
}

.filters-section {
    position: sticky;
    top: 76px;
    z-index: 100;
    background: white !important;
}

@media (max-width: 768px) {
    .filters-section {
        position: relative;
        top: auto;
    }
}
</style>
