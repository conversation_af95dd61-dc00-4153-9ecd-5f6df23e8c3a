<?php
/**
 * South Safari Platform - Installation Test Script
 * 
 * This script tests the basic functionality of the South Safari platform
 * Run this after installation to verify everything is working correctly
 */

// Prevent running in production
if (file_exists('config/config.php')) {
    require_once 'config/config.php';
    if (defined('DEBUG_MODE') && !DEBUG_MODE) {
        die('This test script should not be run in production mode.');
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>South Safari - Installation Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-result { margin: 0.5rem 0; }
        .test-pass { color: #28a745; }
        .test-fail { color: #dc3545; }
        .test-warning { color: #ffc107; }
        .test-section { margin: 2rem 0; }
    </style>
</head>
<body>
    <div class="container my-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">
                            <i class="fas fa-cog me-2"></i>
                            South Safari Platform - Installation Test
                        </h3>
                    </div>
                    <div class="card-body">
                        
                        <?php
                        $allTestsPassed = true;
                        $warnings = 0;
                        
                        function testResult($condition, $message, $isWarning = false) {
                            global $allTestsPassed, $warnings;
                            
                            if ($condition) {
                                echo '<div class="test-result test-pass"><i class="fas fa-check-circle me-2"></i>' . $message . '</div>';
                            } else {
                                if ($isWarning) {
                                    echo '<div class="test-result test-warning"><i class="fas fa-exclamation-triangle me-2"></i>' . $message . '</div>';
                                    $warnings++;
                                } else {
                                    echo '<div class="test-result test-fail"><i class="fas fa-times-circle me-2"></i>' . $message . '</div>';
                                    $allTestsPassed = false;
                                }
                            }
                        }
                        ?>
                        
                        <!-- PHP Requirements -->
                        <div class="test-section">
                            <h5><i class="fas fa-server me-2"></i>PHP Requirements</h5>
                            <?php
                            testResult(version_compare(PHP_VERSION, '7.4.0', '>='), 
                                'PHP Version: ' . PHP_VERSION . ' (Required: 7.4+)');
                            
                            testResult(extension_loaded('pdo'), 'PDO Extension');
                            testResult(extension_loaded('pdo_mysql'), 'PDO MySQL Extension');
                            testResult(extension_loaded('mbstring'), 'mbstring Extension');
                            testResult(extension_loaded('openssl'), 'OpenSSL Extension');
                            testResult(extension_loaded('curl'), 'cURL Extension');
                            testResult(extension_loaded('gd') || extension_loaded('imagick'), 'GD or ImageMagick Extension');
                            testResult(extension_loaded('fileinfo'), 'FileInfo Extension');
                            testResult(extension_loaded('json'), 'JSON Extension');
                            ?>
                        </div>
                        
                        <!-- File System -->
                        <div class="test-section">
                            <h5><i class="fas fa-folder me-2"></i>File System</h5>
                            <?php
                            testResult(file_exists('config/'), 'Config directory exists');
                            testResult(is_writable('config/'), 'Config directory is writable');
                            
                            testResult(file_exists('public/uploads/') || mkdir('public/uploads/', 0755, true), 
                                'Uploads directory exists');
                            testResult(is_writable('public/uploads/'), 'Uploads directory is writable');
                            
                            testResult(file_exists('storage/') || mkdir('storage/', 0755, true), 
                                'Storage directory exists');
                            testResult(is_writable('storage/'), 'Storage directory is writable');
                            
                            testResult(file_exists('storage/logs/') || mkdir('storage/logs/', 0755, true), 
                                'Logs directory exists');
                            testResult(is_writable('storage/logs/'), 'Logs directory is writable');
                            ?>
                        </div>
                        
                        <!-- Configuration -->
                        <div class="test-section">
                            <h5><i class="fas fa-cogs me-2"></i>Configuration</h5>
                            <?php
                            $configExists = file_exists('config/config.php');
                            testResult($configExists, 'Configuration file exists');
                            
                            if ($configExists) {
                                require_once 'config/config.php';
                                
                                testResult(defined('DB_HOST'), 'Database host configured');
                                testResult(defined('DB_NAME'), 'Database name configured');
                                testResult(defined('DB_USER'), 'Database user configured');
                                testResult(defined('SECRET_KEY') && SECRET_KEY !== 'your-secret-key-here-change-this', 
                                    'Secret key configured');
                                testResult(defined('APP_URL'), 'Application URL configured');
                            }
                            ?>
                        </div>
                        
                        <!-- Database Connection -->
                        <div class="test-section">
                            <h5><i class="fas fa-database me-2"></i>Database</h5>
                            <?php
                            if ($configExists) {
                                try {
                                    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
                                    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                                    testResult(true, 'Database connection successful');
                                    
                                    // Check if tables exist
                                    $tables = ['users', 'projects', 'applications', 'categories', 'messages'];
                                    foreach ($tables as $table) {
                                        $stmt = $pdo->query("SHOW TABLES LIKE '" . DB_PREFIX . $table . "'");
                                        testResult($stmt->rowCount() > 0, "Table '{$table}' exists");
                                    }
                                    
                                } catch (PDOException $e) {
                                    testResult(false, 'Database connection failed: ' . $e->getMessage());
                                }
                            } else {
                                testResult(false, 'Cannot test database - configuration file missing');
                            }
                            ?>
                        </div>
                        
                        <!-- Core Files -->
                        <div class="test-section">
                            <h5><i class="fas fa-file-code me-2"></i>Core Files</h5>
                            <?php
                            $coreFiles = [
                                'public/index.php' => 'Main entry point',
                                'src/Core/App.php' => 'Application core',
                                'src/Core/Database.php' => 'Database class',
                                'src/Core/Router.php' => 'Router class',
                                'src/Controllers/HomeController.php' => 'Home controller',
                                'src/Views/layouts/main.php' => 'Main layout',
                                'public/assets/css/main.css' => 'Main stylesheet',
                                'public/assets/js/main.js' => 'Main JavaScript'
                            ];
                            
                            foreach ($coreFiles as $file => $description) {
                                testResult(file_exists($file), $description . ' exists');
                            }
                            ?>
                        </div>
                        
                        <!-- URL Rewriting -->
                        <div class="test-section">
                            <h5><i class="fas fa-link me-2"></i>URL Rewriting</h5>
                            <?php
                            testResult(file_exists('public/.htaccess'), '.htaccess file exists');
                            testResult(function_exists('apache_get_modules') ? 
                                in_array('mod_rewrite', apache_get_modules()) : true, 
                                'mod_rewrite available (or not Apache)', true);
                            ?>
                        </div>
                        
                        <!-- Security -->
                        <div class="test-section">
                            <h5><i class="fas fa-shield-alt me-2"></i>Security</h5>
                            <?php
                            testResult(!file_exists('install/installer.php') || 
                                (defined('DEBUG_MODE') && DEBUG_MODE), 
                                'Installer removed or debug mode enabled', true);
                            
                            testResult(is_readable('config/config.php') && 
                                !is_readable('config/config.php') || 
                                strpos($_SERVER['REQUEST_URI'], 'config/') === false, 
                                'Config directory protected', true);
                            ?>
                        </div>
                        
                        <!-- Test Summary -->
                        <div class="test-section">
                            <hr>
                            <h5><i class="fas fa-clipboard-check me-2"></i>Test Summary</h5>
                            <?php if ($allTestsPassed && $warnings == 0): ?>
                                <div class="alert alert-success">
                                    <h6><i class="fas fa-check-circle me-2"></i>All Tests Passed!</h6>
                                    <p class="mb-0">Your South Safari platform installation is ready to use.</p>
                                </div>
                                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                    <a href="public/" class="btn btn-primary btn-lg">
                                        <i class="fas fa-home me-2"></i>Visit Platform
                                    </a>
                                    <a href="public/admin" class="btn btn-outline-primary btn-lg">
                                        <i class="fas fa-user-shield me-2"></i>Admin Dashboard
                                    </a>
                                </div>
                            <?php elseif ($allTestsPassed && $warnings > 0): ?>
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Tests Passed with Warnings</h6>
                                    <p class="mb-0">Your installation is functional but has <?= $warnings ?> warning(s) that should be addressed.</p>
                                </div>
                                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                    <a href="public/" class="btn btn-warning btn-lg">
                                        <i class="fas fa-home me-2"></i>Visit Platform
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-danger">
                                    <h6><i class="fas fa-times-circle me-2"></i>Tests Failed</h6>
                                    <p class="mb-0">Your installation has critical issues that must be resolved before the platform can be used.</p>
                                </div>
                                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                    <a href="install/installer.php" class="btn btn-danger btn-lg">
                                        <i class="fas fa-redo me-2"></i>Run Installer
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Additional Information -->
                        <div class="test-section">
                            <h6><i class="fas fa-info-circle me-2"></i>Additional Information</h6>
                            <ul class="list-unstyled">
                                <li><strong>PHP Version:</strong> <?= PHP_VERSION ?></li>
                                <li><strong>Server Software:</strong> <?= $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown' ?></li>
                                <li><strong>Document Root:</strong> <?= $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown' ?></li>
                                <li><strong>Current Directory:</strong> <?= __DIR__ ?></li>
                                <li><strong>Test Time:</strong> <?= date('Y-m-d H:i:s T') ?></li>
                            </ul>
                        </div>
                        
                    </div>
                    <div class="card-footer text-muted text-center">
                        <small>
                            <i class="fas fa-mountain me-1"></i>
                            South Safari Platform v1.0.0 - Installation Test
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
